import type { Config } from 'jest'

/**
 * Root Jest Configuration
 *
 * This configuration is for package-level testing (packages/lib, packages/ui).
 * For web app testing (API & services), use apps/web/jest.config.js
 *
 * The two configurations work independently:
 * - Root: Tests shared packages and utilities
 * - Web App: Tests API clients and services with proper mocking
 */
const config: Config = {
  // Use projects to handle different types of packages
  projects: [
    // Lib package - simple Node.js testing
    {
      displayName: 'lib',
      preset: 'ts-jest',
      testEnvironment: 'node',
      testMatch: ['<rootDir>/packages/lib/**/__tests__/**/*.(test|spec).(ts|tsx)'],
      setupFilesAfterEnv: ['<rootDir>/jest.setup.lib.ts'],
      moduleNameMapper: {
        '^@/packages/(.*)$': '<rootDir>/packages/$1',
      },
      transform: {
        '^.+\\.(ts|tsx)$': ['ts-jest', {
          tsconfig: '<rootDir>/tsconfig.json'
        }],
      },
    },
    // UI and App packages - React Native testing with ts-jest
    {
      displayName: 'react-native',
      preset: 'ts-jest',
      testEnvironment: 'jsdom',
      testMatch: [
        '<rootDir>/packages/ui/**/__tests__/**/*.(test|spec).(ts|tsx)',
        '<rootDir>/packages/app/**/__tests__/**/*.(test|spec).(ts|tsx)'
      ],
      setupFilesAfterEnv: ['<rootDir>/jest.setup.rn.ts'],
      moduleNameMapper: {
        '^@/packages/(.*)$': '<rootDir>/packages/$1',
        '^@/ui/(.*)$': '<rootDir>/packages/ui/$1',
        '^@/lib/(.*)$': '<rootDir>/packages/lib/$1',
        '^@/app/(.*)$': '<rootDir>/packages/app/$1',
        '^react-native$': 'react-native-web',
      },
      transform: {
        '^.+\\.(ts|tsx)$': ['ts-jest', {
          tsconfig: {
            jsx: 'react-jsx',
            esModuleInterop: true,
            allowSyntheticDefaultImports: true,
          }
        }],
      },
      transformIgnorePatterns: [
        'node_modules/(?!(react-native|@react-native|react-native-.*|@react-navigation|react-navigation|nativewind)/)',
      ],
    }
  ],

  // Global settings
  testPathIgnorePatterns: [
    '<rootDir>/.next/',
    '<rootDir>/node_modules/',
    '<rootDir>/apps/web/__tests__/',
    '<rootDir>/apps/web/library/services/__tests__/',
    '<rootDir>/apps/web/app/',
  ],

  // File extensions
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],

  // Coverage for packages only
  collectCoverageFrom: [
    'packages/**/*.{ts,tsx}',
    '!**/*.d.ts',
    '!**/node_modules/**',
    '!**/__tests__/**',
  ],

  // Coverage thresholds for packages
  coverageThreshold: {
    global: {
      branches: 50,
      functions: 50,
      lines: 50,
      statements: 50,
    },
  },

  // Clear mocks between tests
  clearMocks: true,
}

export default config