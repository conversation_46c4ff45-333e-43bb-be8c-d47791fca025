// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema


generator client {
  provider = "prisma-client-js"
}


datasource db {
  provider  = "postgresql"
  url       = env("POSTGRES_DATABASE_URL")
  directUrl = env("POSTGRES_DIRECT_URL") 
}

enum BusinessRegistrationStatus {
  REGISTERED
  UNREGISTERED
  PENDING
}

enum BusinessSystemStatus {
  ACTIVE
  SUSPENDED
  DEREGISTERED
}

enum BusinessStatus {
  ACTIVE
  SOLD
  CLOSED
  MERGED
  BANKRUPT
}

enum ScopeType {
  BUSINESS
  BUSINESS_GROUP
  USER
  SYSTEM
}

enum ResourceType {
  NOTE
  DOCUMENT
  SECUREVAULT
}

enum ComplianceStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  OVERDUE
}

// ============================================================================
// SCOPE
// ============================================================================

model Scope {                              // Represent a scope for a role e.g business, business group
  id                      String           @id @default(uuid())
  type                    ScopeType          
  entityId                String          // ID of Business, BusinessGroup, User, etc.
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt
  
  
  roleAssignments         RoleAssignment[]
  permissionResourceRelationships PermissionResourceRelationship[]
  addressRelationships    AddressRelationship[]
  businessPresences       BusinessPresence[]
}

// ============================================================================
// USER
// ============================================================================
model User {
  id                      String           @id @default(uuid())
  firstName               String
  lastName                String
  clerkUserId             String            @unique
  phone                   String
  language                String           // en, fr, es, etc.
  timezone                String           // Africa/Lagos, America/New_York, etc.
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt
  
  
  userSettings            userSetting[]
  emailAddresses          EmailAddresses[]
  roleAssignments         RoleAssignment[] @relation("AssignedTo")
  assignedRoleAssignments RoleAssignment[] @relation("AssignedBy")
  rolesCreated            Role[]           @relation("CreatedBy")
  permissionAssignments   PermissionResourceRelationship[] @relation("AssignedBy")
  documents               Document[]       @relation("UploadedBy")
  notes                   Note[]
  secureVaults            SecureVault[]
  integrations            Integration[]
  businessMemberships     UserBusinessMembership[]

  @@index([phone])
}

// ============================================================================
// USER SETTING
// ============================================================================
model userSetting {
  id                      String           @id @default(uuid())
  userId                  String
  data                    Json
  createdAt  DateTime       @default(now())
  updatedAt  DateTime       @updatedAt
  
  user                    User             @relation(fields: [userId], references: [id])
}

// ============================================================================
// USER EMAIL ADDRESS
// ============================================================================
model EmailAddresses {
  id                      String           @id @default(uuid())
  userId                  String
  emailAddress            String           @unique
  verified                Boolean          @default(false)
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt
  

  user                    User             @relation(fields: [userId], references: [id])
  preferences             EmailPreferences[]
}

// ============================================================================
// EMAIL ADDRESS PREFERENCE CONFIGURATION
// ============================================================================
model EmailPreferences {
  emailId                 String
  businessId              String
  email                   EmailAddresses   @relation(fields: [emailId], references: [id])
  business                Business         @relation(fields: [businessId], references: [id])
  createdAt  DateTime       @default(now())
  updatedAt  DateTime       @updatedAt
  
  @@id([emailId, businessId])
}

// ============================================================================
// BUSINESS
// ============================================================================
model Business {
  id                      String                   @id @default(uuid())
  name                    String
  type                    String?                   // branch, subsidiary, HQ - can be optional
  countryId               String
  registrationStatus      BusinessRegistrationStatus @default(UNREGISTERED)           // status as related to a business's country registration process
  systemStatus            BusinessSystemStatus     @default(ACTIVE)                   //  status as related to ventureDirection 
  businessStatus          BusinessStatus           @default(ACTIVE)                   //  status as related to the business
  businessStatusUpdatedAt DateTime                 // date of last Business.businessStatus change
  registrationNumber      String                   // registration number with a country's registrar
  incorporationDate       DateTime                 // date of incorporation
  createdAt               DateTime                 @default(now())
  updatedAt               DateTime                 @updatedAt
  settings                Json                      // precedence over parent setting(i.e BusinessGroup) and can contains e.g languages, logo, currency, timezone e.t.c

  country                 Country                  @relation(fields: [countryId], references: [id])
  emailPreferences        EmailPreferences[]
  compliance              Compliance[]
  businessIndustries      BusinessIndustry[]
  businessWithGroupRelationships BusinessWithGroupRelationship[]
  subscriptions           Subscription[]
  notes                   Note[]
  secureVaults            SecureVault[]
  integrations            Integration[]
  userMemberships         UserBusinessMembership[]
  onboardingProgress      BusinessOnboardingProgress[]

  // Store & Products
  products                Product[]
  productVariants         ProductVariant[]

  // Orders & Sales
  orders                  Order[]
  orderItems              OrderItem[]

  // Finance
  payments                Payment[]
  invoices                Invoice[]
  invoiceItems            InvoiceItem[]

  // CRM
  customers               Customer[]
  customerInteractions    CustomerInteraction[]
  leads                   Lead[]

  // Marketing & Analytics
  campaigns               Campaign[]
  analyticsEvents         AnalyticsEvent[]
  businessMetrics         BusinessMetric[]

  // Logistics & Inventory
  inventoryMovements      InventoryMovement[]
  suppliers               Supplier[]
  purchaseOrders          PurchaseOrder[]
  purchaseOrderItems      PurchaseOrderItem[]

  // Index for query optimization - quick lookups
  @@index([name])
  @@index([registrationNumber])
}


// ============================================================================
// REPRESENT CURRENT USER BUSINESS CONTEXT - USED FOR SESSION CONTEXT
// ============================================================================
model UserBusinessContext {
  id                          String           @id @default(uuid())
  userId                      String           @unique
  activeBusinessId            String           // this will change when user switches business
  activeBusinessGroupId       String           // this will change when user switches business group
  accessibleBusinessIds       String[]         // all businesses user has access to
  accessibleBusinessGroupIds  String[]         // all business groups user has access to
  lastSwitchedAt              DateTime         // Track last business switch time
  
  createdAt                   DateTime         @default(now())
  updatedAt                   DateTime         @updatedAt
}

// ============================================================================
// REPRESENT BUSINESS SOCIAL MEDIAL ACCOUNTS
// ============================================================================
model BusinessPresence {                   // Represent a Business Social Media accounts
  id                      String           @id @default(uuid())
  scopeId                 String
  platform                String           //e.g. 'instagram', 'whatsapp', 'tiktok'
  handle                  String
  externalUrl             String
  verified                Boolean
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt
  
  
  scope                   Scope            @relation(fields: [scopeId], references: [id])
}

// ============================================================================
// VENTURE DIRECT LIST OF SUPPORTED INDUSTRIES
// ============================================================================
model Industries {                        // ventureDirection list of supported industries.
  id                      String           @id @default(uuid())
  name                    String           @unique           // E.g Information Technology & Services, HealthCare, Agriculture e.t.c 
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt
  businessIndustries      BusinessIndustry[]
}


model BusinessIndustry {                   // Link a business to our internal supported industries.
  id                      String           @id @default(uuid())
  businessId              String
  industryId              String          
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt
  
  business                Business         @relation(fields: [businessId], references: [id])
  industry                Industries       @relation(fields: [industryId], references: [id])

  @@unique([businessId, industryId])
}


model BusinessGroup {
  id                      String                   @id @default(uuid())
  name                    String
  parentId                String?                  // Pointing to another top level business group as a parent
  settings                Json
  createdAt               DateTime                 @default(now())
  updatedAt               DateTime                 @updatedAt
  
  businessWithGroupRelationships BusinessWithGroupRelationship[]
  userMemberships         UserBusinessMembership[]
}


model BusinessWithGroupRelationship {      // link  business to group
  id                      String           @id @default(uuid())
  businessGroupId         String
  businessId              String
  businessGroup           BusinessGroup    @relation(fields: [businessGroupId], references: [id])
  business                Business         @relation(fields: [businessId], references: [id])

  @@unique([businessGroupId, businessId])
}


model UserBusinessMembership {             // Optimized view for user-business relationships and context switching
  id                      String           @id @default(uuid())
  userId                  String
  businessId              String
  businessGroupId         String?
  roleTitle               String           // Cached role title for UI display
  isActive                Boolean          @default(true)
  lastAccessedAt          DateTime?        // Track usage patterns
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt

  user                    User             @relation(fields: [userId], references: [id])
  business                Business         @relation(fields: [businessId], references: [id])
  businessGroup           BusinessGroup?   @relation(fields: [businessGroupId], references: [id])

  @@unique([userId, businessId])
  @@index([userId, isActive])
  @@index([userId, businessGroupId])
  @@index([lastAccessedAt])
}


model BusinessOnboardingProgress {     // Track business onboarding completion per feature module
  id                      String           @id @default(uuid())
  businessId              String
  featureModule           String           // 'store', 'orders', 'finance', 'crm', 'compliance', 'documents', 'settings'
  completed               Boolean          @default(false)
  completedAt             DateTime?
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt

  business                Business         @relation(fields: [businessId], references: [id], onDelete: Cascade)

  @@unique([businessId, featureModule])
  @@index([businessId])
  @@index([businessId, completed])
}


// ============================================================================
// STORE & PRODUCTS
// ============================================================================

model Product {
  id                      String           @id @default(uuid())
  businessId              String
  name                    String
  description             String?
  sku                     String?          // Stock Keeping Unit
  barcode                 String?
  category                String?
  brand                   String?

  // Pricing
  price                   Decimal          @db.Decimal(10, 2)
  costPrice               Decimal?         @db.Decimal(10, 2)
  compareAtPrice          Decimal?         @db.Decimal(10, 2)  // Original price for discounts

  // Inventory
  trackInventory          Boolean          @default(true)
  inventoryQuantity       Int              @default(0)
  lowStockThreshold       Int?
  allowBackorder          Boolean          @default(false)

  // Physical properties
  weight                  Decimal?         @db.Decimal(8, 3)
  weightUnit              String?          // kg, lb, etc.
  dimensions              Json?            // {length, width, height, unit}

  // Status and visibility
  status                  ProductStatus    @default(DRAFT)
  isActive                Boolean          @default(true)
  isFeatured              Boolean          @default(false)

  // SEO and metadata
  slug                    String?
  metaTitle               String?
  metaDescription         String?
  tags                    String[]

  // Media
  images                  String[]         // Array of image URLs
  thumbnail               String?

  // Timestamps
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt
  publishedAt             DateTime?

  // Relations
  business                Business         @relation(fields: [businessId], references: [id], onDelete: Cascade)
  variants                ProductVariant[]
  orderItems              OrderItem[]
  inventoryMovements      InventoryMovement[]
  invoiceItems            InvoiceItem[]
  purchaseOrderItems      PurchaseOrderItem[]

  @@unique([businessId, sku])
  @@index([businessId])
  @@index([businessId, status])
  @@index([businessId, isActive])
  @@index([sku])
  @@index([category])
}

enum ProductStatus {
  DRAFT
  ACTIVE
  ARCHIVED
  OUT_OF_STOCK
}

model ProductVariant {
  id                      String           @id @default(uuid())
  productId               String
  businessId              String
  name                    String
  sku                     String?
  barcode                 String?

  // Pricing (can override product pricing)
  price                   Decimal?         @db.Decimal(10, 2)
  costPrice               Decimal?         @db.Decimal(10, 2)
  compareAtPrice          Decimal?         @db.Decimal(10, 2)

  // Inventory
  inventoryQuantity       Int              @default(0)

  // Variant options (e.g., size: "Large", color: "Red")
  options                 Json             // {size: "Large", color: "Red"}

  // Physical properties
  weight                  Decimal?         @db.Decimal(8, 3)
  dimensions              Json?

  // Media
  image                   String?

  // Status
  isActive                Boolean          @default(true)

  // Timestamps
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt

  // Relations
  product                 Product          @relation(fields: [productId], references: [id], onDelete: Cascade)
  business                Business         @relation(fields: [businessId], references: [id], onDelete: Cascade)
  orderItems              OrderItem[]
  inventoryMovements      InventoryMovement[]

  @@unique([businessId, sku])
  @@index([productId])
  @@index([businessId])
}


// ============================================================================
// ORDERS & SALES
// ============================================================================

model Order {
  id                      String           @id @default(uuid())
  businessId              String
  orderNumber             String           // Human-readable order number

  // Customer information
  customerId              String?
  customerEmail           String?
  customerPhone           String?
  customerName            String?

  // Order details
  status                  OrderStatus      @default(PENDING)
  type                    OrderType        @default(SALE)
  channel                 OrderChannel     @default(ONLINE)

  // Financial
  subtotal                Decimal          @db.Decimal(10, 2)
  taxAmount               Decimal          @default(0) @db.Decimal(10, 2)
  shippingAmount          Decimal          @default(0) @db.Decimal(10, 2)
  discountAmount          Decimal          @default(0) @db.Decimal(10, 2)
  totalAmount             Decimal          @db.Decimal(10, 2)
  currency                String           @default("USD")

  // Payment
  paymentStatus           PaymentStatus    @default(PENDING)
  paymentMethod           String?
  paymentReference        String?
  paidAt                  DateTime?

  // Shipping
  shippingAddress         Json?            // Full address object
  billingAddress          Json?            // Full address object
  shippingMethod          String?
  trackingNumber          String?
  shippedAt               DateTime?
  deliveredAt             DateTime?

  // Metadata
  notes                   String?
  internalNotes           String?
  tags                    String[]
  source                  String?          // Website, POS, API, etc.

  // Timestamps
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt
  cancelledAt             DateTime?

  // Relations
  business                Business         @relation(fields: [businessId], references: [id], onDelete: Cascade)
  customer                Customer?        @relation(fields: [customerId], references: [id])
  items                   OrderItem[]
  payments                Payment[]
  invoices                Invoice[]

  @@unique([businessId, orderNumber])
  @@index([businessId])
  @@index([businessId, status])
  @@index([businessId, createdAt])
  @@index([customerId])
  @@index([customerEmail])
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

enum OrderType {
  SALE
  RETURN
  EXCHANGE
  QUOTE
}

enum OrderChannel {
  ONLINE
  POS
  PHONE
  EMAIL
  MARKETPLACE
  SOCIAL
}

enum PaymentStatus {
  PENDING
  PAID
  PARTIALLY_PAID
  FAILED
  REFUNDED
  CANCELLED
}

model OrderItem {
  id                      String           @id @default(uuid())
  orderId                 String
  businessId              String
  productId               String?
  productVariantId        String?

  // Product details (snapshot at time of order)
  productName             String
  productSku              String?
  variantName             String?

  // Pricing
  unitPrice               Decimal          @db.Decimal(10, 2)
  quantity                Int
  totalPrice              Decimal          @db.Decimal(10, 2)

  // Metadata
  notes                   String?

  // Timestamps
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt

  // Relations
  order                   Order            @relation(fields: [orderId], references: [id], onDelete: Cascade)
  business                Business         @relation(fields: [businessId], references: [id], onDelete: Cascade)
  product                 Product?         @relation(fields: [productId], references: [id])
  productVariant          ProductVariant?  @relation(fields: [productVariantId], references: [id])

  @@index([orderId])
  @@index([businessId])
  @@index([productId])
}


// ============================================================================
// FINANCE & PAYMENTS
// ============================================================================

model Payment {
  id                      String           @id @default(uuid())
  businessId              String
  orderId                 String?
  customerId              String?

  // Payment details
  amount                  Decimal          @db.Decimal(10, 2)
  currency                String           @default("USD")
  status                  PaymentStatus    @default(PENDING)
  method                  PaymentMethod    @default(CARD)

  // External references
  externalId              String?          // Stripe, PayPal, etc. payment ID
  gatewayProvider         String?          // stripe, paypal, square, etc.
  gatewayResponse         Json?            // Full gateway response

  // Transaction details
  reference               String?          // Internal reference
  description             String?
  notes                   String?

  // Timestamps
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt
  processedAt             DateTime?
  failedAt                DateTime?

  // Relations
  business                Business         @relation(fields: [businessId], references: [id], onDelete: Cascade)
  order                   Order?           @relation(fields: [orderId], references: [id])
  customer                Customer?        @relation(fields: [customerId], references: [id])

  @@index([businessId])
  @@index([businessId, status])
  @@index([orderId])
  @@index([externalId])
}

enum PaymentMethod {
  CARD
  BANK_TRANSFER
  CASH
  CHECK
  PAYPAL
  PAYSTACK
  FLUTTERWAVE
  STRIPE
  MOBILE_MONEY
  CRYPTO
  OTHER
}

model Invoice {
  id                      String           @id @default(uuid())
  businessId              String
  customerId              String?
  orderId                 String?

  // Invoice details
  invoiceNumber           String           // Human-readable invoice number
  status                  InvoiceStatus    @default(DRAFT)
  type                    InvoiceType      @default(INVOICE)

  // Financial
  subtotal                Decimal          @db.Decimal(10, 2)
  taxAmount               Decimal          @default(0) @db.Decimal(10, 2)
  discountAmount          Decimal          @default(0) @db.Decimal(10, 2)
  totalAmount             Decimal          @db.Decimal(10, 2)
  paidAmount              Decimal          @default(0) @db.Decimal(10, 2)
  currency                String           @default("USD")

  // Dates
  issueDate               DateTime         @default(now())
  dueDate                 DateTime?
  paidDate                DateTime?

  // Content
  description             String?
  notes                   String?
  terms                   String?

  // Addresses
  billingAddress          Json?
  shippingAddress         Json?

  // Timestamps
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt

  // Relations
  business                Business         @relation(fields: [businessId], references: [id], onDelete: Cascade)
  customer                Customer?        @relation(fields: [customerId], references: [id])
  order                   Order?           @relation(fields: [orderId], references: [id])
  items                   InvoiceItem[]

  @@unique([businessId, invoiceNumber])
  @@index([businessId])
  @@index([businessId, status])
  @@index([customerId])
  @@index([dueDate])
}

enum InvoiceStatus {
  DRAFT
  SENT
  VIEWED
  PAID
  OVERDUE
  CANCELLED
}

enum InvoiceType {
  INVOICE
  QUOTE
  ESTIMATE
  RECEIPT
  CREDIT_NOTE
}

model InvoiceItem {
  id                      String           @id @default(uuid())
  invoiceId               String
  businessId              String

  // Item details
  description             String
  quantity                Decimal          @db.Decimal(10, 3)
  unitPrice               Decimal          @db.Decimal(10, 2)
  totalPrice              Decimal          @db.Decimal(10, 2)

  // Optional product reference
  productId               String?
  productSku              String?

  // Timestamps
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt

  // Relations
  invoice                 Invoice          @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  business                Business         @relation(fields: [businessId], references: [id], onDelete: Cascade)
  product                 Product?         @relation(fields: [productId], references: [id])

  @@index([invoiceId])
  @@index([businessId])
}


// ============================================================================
// CRM & CUSTOMERS
// ============================================================================

model Customer {
  id                      String           @id @default(uuid())
  businessId              String

  // Basic information
  firstName               String?
  lastName                String?
  email                   String?
  phone                   String?
  company                 String?

  // Status
  status                  CustomerStatus   @default(ACTIVE)
  type                    CustomerType     @default(INDIVIDUAL)

  // Contact details
  addresses               Json[]           // Array of address objects
  socialProfiles          Json?            // Social media profiles
  website                 String?

  // Business details
  taxId                   String?          // Tax identification number
  vatNumber               String?          // VAT number for EU businesses

  // Preferences
  preferredLanguage       String?
  preferredCurrency       String?
  marketingOptIn          Boolean          @default(false)

  // Metadata
  notes                   String?
  tags                    String[]
  source                  String?          // How they found you

  // Timestamps
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt
  lastContactedAt         DateTime?

  // Relations
  business                Business         @relation(fields: [businessId], references: [id], onDelete: Cascade)
  orders                  Order[]
  payments                Payment[]
  invoices                Invoice[]
  interactions            CustomerInteraction[]
  analyticsEvents         AnalyticsEvent[]

  @@unique([businessId, email])
  @@index([businessId])
  @@index([businessId, status])
  @@index([email])
  @@index([phone])
  @@index([company])
}

enum CustomerStatus {
  ACTIVE
  INACTIVE
  PROSPECT
  BLOCKED
}

enum CustomerType {
  INDIVIDUAL
  BUSINESS
  ORGANIZATION
}

model CustomerInteraction {
  id                      String           @id @default(uuid())
  businessId              String
  customerId              String

  // Interaction details
  type                    InteractionType
  subject                 String?
  description             String?
  outcome                 String?

  // Metadata
  channel                 InteractionChannel?
  duration                Int?             // Duration in minutes
  attachments             String[]         // File URLs

  // Timestamps
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt
  scheduledAt             DateTime?
  completedAt             DateTime?

  // Relations
  business                Business         @relation(fields: [businessId], references: [id], onDelete: Cascade)
  customer                Customer         @relation(fields: [customerId], references: [id], onDelete: Cascade)

  @@index([businessId])
  @@index([customerId])
  @@index([businessId, type])
}

enum InteractionType {
  CALL
  EMAIL
  MEETING
  NOTE
  TASK
  FOLLOW_UP
  SUPPORT
  SALES
}

enum InteractionChannel {
  PHONE
  EMAIL
  IN_PERSON
  VIDEO_CALL
  CHAT
  SOCIAL_MEDIA
  WEBSITE
}

model Lead {
  id                      String           @id @default(uuid())
  businessId              String

  // Lead information
  firstName               String?
  lastName                String?
  email                   String?
  phone                   String?
  company                 String?

  // Lead details
  status                  LeadStatus       @default(NEW)
  source                  String?
  score                   Int?             // Lead scoring
  value                   Decimal?         @db.Decimal(10, 2)

  // Qualification
  budget                  Decimal?         @db.Decimal(10, 2)
  timeline                String?
  authority               String?
  need                    String?

  // Metadata
  notes                   String?
  tags                    String[]

  // Timestamps
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt
  qualifiedAt             DateTime?
  convertedAt             DateTime?

  // Relations
  business                Business         @relation(fields: [businessId], references: [id], onDelete: Cascade)

  @@index([businessId])
  @@index([businessId, status])
  @@index([email])
}

enum LeadStatus {
  NEW
  CONTACTED
  QUALIFIED
  PROPOSAL
  NEGOTIATION
  CONVERTED
  LOST
}


// ============================================================================
// MARKETING & CAMPAIGNS
// ============================================================================

model Campaign {
  id                      String           @id @default(uuid())
  businessId              String

  // Campaign details
  name                    String
  description             String?
  type                    CampaignType
  status                  CampaignStatus   @default(DRAFT)

  // Targeting
  targetAudience          Json?            // Audience criteria
  segmentIds              String[]         // Customer segments

  // Content
  subject                 String?          // Email subject or campaign title
  content                 String?          // Campaign content/message
  callToAction            String?
  landingPageUrl          String?

  // Budget and goals
  budget                  Decimal?         @db.Decimal(10, 2)
  goalType                String?          // clicks, conversions, revenue
  goalValue               Decimal?         @db.Decimal(10, 2)

  // Scheduling
  startDate               DateTime?
  endDate                 DateTime?

  // Metrics
  impressions             Int              @default(0)
  clicks                  Int              @default(0)
  conversions             Int              @default(0)
  revenue                 Decimal          @default(0) @db.Decimal(10, 2)

  // Timestamps
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt
  launchedAt              DateTime?

  // Relations
  business                Business         @relation(fields: [businessId], references: [id], onDelete: Cascade)

  @@index([businessId])
  @@index([businessId, status])
  @@index([businessId, type])
}

enum CampaignType {
  EMAIL
  SMS
  SOCIAL_MEDIA
  PAID_ADS
  CONTENT
  EVENT
  REFERRAL
  LOYALTY
}

enum CampaignStatus {
  DRAFT
  SCHEDULED
  ACTIVE
  PAUSED
  COMPLETED
  CANCELLED
}


// ============================================================================
// ANALYTICS & REPORTING
// ============================================================================

model AnalyticsEvent {
  id                      String           @id @default(uuid())
  businessId              String

  // Event details
  eventType               String           // page_view, purchase, signup, etc.
  eventName               String?
  category                String?

  // Context
  userId                  String?          // Internal user ID
  sessionId               String?
  customerId              String?

  // Data
  properties              Json?            // Event-specific data
  value                   Decimal?         @db.Decimal(10, 2)
  currency                String?

  // Source tracking
  source                  String?          // utm_source
  medium                  String?          // utm_medium
  campaign                String?          // utm_campaign
  referrer                String?

  // Technical details
  userAgent               String?
  ipAddress               String?
  country                 String?
  city                    String?

  // Timestamps
  createdAt               DateTime         @default(now())
  eventTimestamp          DateTime         @default(now())

  // Relations
  business                Business         @relation(fields: [businessId], references: [id], onDelete: Cascade)
  customer                Customer?        @relation(fields: [customerId], references: [id])

  @@index([businessId])
  @@index([businessId, eventType])
  @@index([businessId, eventTimestamp])
  @@index([sessionId])
  @@index([customerId])
}

model BusinessMetric {
  id                      String           @id @default(uuid())
  businessId              String

  // Metric details
  metricType              String           // revenue, orders, customers, etc.
  metricName              String
  value                   Decimal          @db.Decimal(15, 4)
  unit                    String?          // currency, count, percentage, etc.

  // Time period
  period                  MetricPeriod
  periodStart             DateTime
  periodEnd               DateTime

  // Metadata
  dimensions              Json?            // Additional breakdown data

  // Timestamps
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt

  // Relations
  business                Business         @relation(fields: [businessId], references: [id], onDelete: Cascade)

  @@unique([businessId, metricType, metricName, period, periodStart])
  @@index([businessId])
  @@index([businessId, metricType])
  @@index([businessId, period, periodStart])
}

enum MetricPeriod {
  HOUR
  DAY
  WEEK
  MONTH
  QUARTER
  YEAR
}


// ============================================================================
// LOGISTICS & INVENTORY
// ============================================================================

model InventoryMovement {
  id                      String           @id @default(uuid())
  businessId              String
  productId               String
  productVariantId        String?

  // Movement details
  type                    MovementType
  quantity                Int              // Positive for inbound, negative for outbound
  reason                  String?
  reference               String?          // Order ID, adjustment ID, etc.

  // Cost tracking
  unitCost                Decimal?         @db.Decimal(10, 2)
  totalCost               Decimal?         @db.Decimal(10, 2)

  // Location
  locationFrom            String?
  locationTo              String?

  // Metadata
  notes                   String?

  // Timestamps
  createdAt               DateTime         @default(now())
  movementDate            DateTime         @default(now())

  // Relations
  business                Business         @relation(fields: [businessId], references: [id], onDelete: Cascade)
  product                 Product          @relation(fields: [productId], references: [id], onDelete: Cascade)
  productVariant          ProductVariant?  @relation(fields: [productVariantId], references: [id])

  @@index([businessId])
  @@index([productId])
  @@index([businessId, movementDate])
  @@index([businessId, type])
}

enum MovementType {
  PURCHASE          // Stock received from supplier
  SALE             // Stock sold to customer
  ADJUSTMENT       // Manual inventory adjustment
  TRANSFER         // Transfer between locations
  RETURN           // Customer return
  DAMAGE           // Damaged goods write-off
  THEFT            // Theft/loss
  PRODUCTION       // Manufacturing/assembly
  CONSUMPTION      // Raw materials used in production
}

model Supplier {
  id                      String           @id @default(uuid())
  businessId              String

  // Basic information
  name                    String
  email                   String?
  phone                   String?
  website                 String?

  // Contact person
  contactName             String?
  contactEmail            String?
  contactPhone            String?

  // Business details
  taxId                   String?
  vatNumber               String?

  // Address
  address                 Json?            // Full address object

  // Terms
  paymentTerms            String?          // Net 30, etc.
  currency                String           @default("USD")

  // Status
  status                  SupplierStatus   @default(ACTIVE)

  // Metadata
  notes                   String?
  tags                    String[]

  // Timestamps
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt

  // Relations
  business                Business         @relation(fields: [businessId], references: [id], onDelete: Cascade)
  purchaseOrders          PurchaseOrder[]

  @@index([businessId])
  @@index([businessId, status])
}

enum SupplierStatus {
  ACTIVE
  INACTIVE
  BLOCKED
}

model PurchaseOrder {
  id                      String           @id @default(uuid())
  businessId              String
  supplierId              String

  // Order details
  orderNumber             String           // Human-readable PO number
  status                  PurchaseOrderStatus @default(DRAFT)

  // Financial
  subtotal                Decimal          @db.Decimal(10, 2)
  taxAmount               Decimal          @default(0) @db.Decimal(10, 2)
  shippingAmount          Decimal          @default(0) @db.Decimal(10, 2)
  totalAmount             Decimal          @db.Decimal(10, 2)
  currency                String           @default("USD")

  // Dates
  orderDate               DateTime         @default(now())
  expectedDate            DateTime?
  receivedDate            DateTime?

  // Shipping
  shippingAddress         Json?

  // Metadata
  notes                   String?
  terms                   String?

  // Timestamps
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt

  // Relations
  business                Business         @relation(fields: [businessId], references: [id], onDelete: Cascade)
  supplier                Supplier         @relation(fields: [supplierId], references: [id])
  items                   PurchaseOrderItem[]

  @@unique([businessId, orderNumber])
  @@index([businessId])
  @@index([businessId, status])
  @@index([supplierId])
}

enum PurchaseOrderStatus {
  DRAFT
  SENT
  CONFIRMED
  PARTIALLY_RECEIVED
  RECEIVED
  CANCELLED
}

model PurchaseOrderItem {
  id                      String           @id @default(uuid())
  purchaseOrderId         String
  businessId              String
  productId               String?

  // Item details
  description             String
  sku                     String?
  quantity                Int
  unitCost                Decimal          @db.Decimal(10, 2)
  totalCost               Decimal          @db.Decimal(10, 2)

  // Receiving
  quantityReceived        Int              @default(0)

  // Timestamps
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt

  // Relations
  purchaseOrder           PurchaseOrder    @relation(fields: [purchaseOrderId], references: [id], onDelete: Cascade)
  business                Business         @relation(fields: [businessId], references: [id], onDelete: Cascade)
  product                 Product?         @relation(fields: [productId], references: [id])

  @@index([purchaseOrderId])
  @@index([businessId])
}


model RoleAssignment {                     // Link a user to a role within a scope
  id                      String           @id @default(uuid())
  userId                  String           // User to be assigned the role
  roleId                  String           // Role to be assigned
  scopeId                 String           // Scope within which the role is assigned
  assignedBy              String?          // User who assigned the role and can be null for assignment from system (i.e assignment from ventureDirection)
  revokedAt               DateTime?        // Date when the role was revoked
  isActive                Boolean          @default(true)
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt


  user                    User             @relation("AssignedTo", fields: [userId], references: [id])
  role                    Role             @relation(fields: [roleId], references: [id])
  assigner                User?             @relation("AssignedBy", fields: [assignedBy], references: [id])
  scope                   Scope            @relation(fields: [scopeId], references: [id])

  @@index([isActive])
  @@index([userId, scopeId])
   @@index([userId, scopeId, isActive])
}


model RolePermission {                     // Link a role to a permission
  id                      String           @id @default(uuid())
  roleId                  String
  permissionId            String
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt
  
  
  role                    Role             @relation(fields: [roleId], references: [id])
  permission              Permissions      @relation(fields: [permissionId], references: [id])
}


model Role {                               // Represent a role within a scope e.g business, business group 
  id                      String           @id @default(uuid())
  title                   String           // role name
  type                    String           // role type e.g. 'business', 'business group' or 'system'
  creatorId               String?          // User who created the role and optionally can be null for system roles (i.e roles created by ventureDirection)
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt
  creator                 User?             @relation("CreatedBy", fields: [creatorId], references: [id])
  roleAssignments         RoleAssignment[]
  rolePermissions         RolePermission[]
  parentRoles             RoleHierarchy[]  @relation("ChildRole")
  childRoles              RoleHierarchy[]     @relation("ParentRole")
}


model RoleHierarchy {                      // Represent a role hierarchy - a parent role inherits all permissions of a child role
  id                      String           @id @default(uuid())
  parentRoleId            String           // Pointing to parent role.id
  childRoleId             String           // Pointing to child role.id
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt
  
  parentRole              Role             @relation("ParentRole", fields: [parentRoleId], references: [id])
  childRole               Role             @relation("ChildRole", fields: [childRoleId], references: [id])
}


model Permissions {                                // Represent a permission
  id                      String                   @id @default(uuid())
  name                    String                   // permission name e.g. 'canCreateBusiness',  'canDeleteBusiness' canTransferOwnership, 
  createdAt               DateTime                 @default(now())
  updatedAt               DateTime                 @updatedAt
  rolePermissions         RolePermission[]
  permissionResourceRelationships PermissionResourceRelationship[]
}


model PermissionResourceRelationship {     // Link a custom permission and optional conditions to a specific resource entity (i.e Note, Document, SecureVault)
  id                      String           @id @default(uuid())
  permissionId            String           // Pointing to permission.id
  resourceType            ResourceType           
  resourceId              String           // Pointing to Note.id or Document.id or SecureVault.id
  scopeId                 String             // point to Scope within which the permission is assigned (i.e Business, BusinessGroup)
  conditions              Json?             // optional conditions for the permission e.g. 'canReadSecureVault' with condition 'only for users with role 'Head_of_Security'  
  assignedById            String           // User who assigned the permission
  revokedAt               DateTime?        // Date when the permission was revoked
  isActive                Boolean          @default(true)
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt
  
  
  permission              Permissions      @relation(fields: [permissionId], references: [id])
  assignedBy              User             @relation("AssignedBy", fields: [assignedById], references: [id])
  scope                   Scope            @relation(fields: [scopeId], references: [id])

  @@index([isActive])
  @@index([resourceType, resourceId], map: "resource_type_resource_id")
  @@index([resourceType, resourceId, scopeId], map: "resource_type_resource_id_scope_id")
  @@index([resourceType, resourceId, isActive], map: "resource_type_resource_id_isactive")
  @@index([resourceType, resourceId, scopeId, isActive], map: "resource_type_resource_id_scope_id_isactive")
}


model Country {                                // Represent a country
  id                      String               @id @default(uuid())
  name                    String               @unique // country name
  code                    String               @unique // ISO 3166-1 alpha-2 code
  currency                String               // ISO 4217 currency code
  language                String               // Official language
  timezone                String               // Timezone
  createdAt               DateTime             @default(now())
  updatedAt               DateTime             @updatedAt
  
  
  businesses              Business[]
  complianceTemplates     ComplianceTemplate[]
}


model Compliance {                           // Represent a business or in rare case a business group compliance.
  id                      String             @id @default(uuid())
  businessId              String             // Pointing to business.id
  templateId              String             // Pointing to compliance template.id
  status                  ComplianceStatus   @default(PENDING)           // status of the business specific compliance 
  dueDate                 DateTime?          // Date when the compliance is due
  penalty                 String             // Penalty for not completing the compliance
  responses               Json               // Responses to the compliance questions (i.e ComplianceRules)
  createdAt               DateTime           @default(now())
  updatedAt               DateTime           @updatedAt
  
  business                Business           @relation(fields: [businessId], references: [id])
  template                ComplianceTemplate @relation(fields: [templateId], references: [id])
  
  //  Index for query optimization - quick lookups
  @@index([dueDate])
  @@index([businessId]) 
  @@index([templateId])
  @@index([status])
  @@index([businessId, templateId])
  @@index([businessId, templateId, status])
  @@index([businessId, templateId, dueDate]) 
}


model ComplianceTemplate {                   // model to represent a specific compliance of a regulatory body for a specific country
  id                      String             @id @default(uuid())
  countryId               String             // Pointing to country.id 
  name                    String             // e.g. 'CAC_COMPANY_REGISTRATION_FORM - (applicable to Nigeria businesses)', 'Tax_Compliance',  
  description             String             // Short description of the compliance
  frequency               String             // e.g. 'Annual', 'Quarterly', 'Monthly', 'OneTime'
  regulatoryBody          String             // e.g. 'CAC', 'IRS', 'SEC', 'FINRA', 'NDLEA', 'FIRS', 'HIPAA' e.t.c
  penalty                 String             // Penalty for not completing the compliance
  timeline                String             // e.g. 'Before', 'After'
  category                String             // e.g. 'Registration', 'Tax', 'Compliance', 'Reporting'
  createdAt               DateTime           @default(now())
  updatedAt               DateTime           @updatedAt
  country                 Country            @relation(fields: [countryId], references: [id])
  

  compliance              Compliance[]
  rules                   ComplianceRule[]

  // Index for query optimization - quick lookups
  @@index([countryId])
  @@index([countryId, name])

}


model ComplianceRule {                       // Represent a rule within a compliance template
  id                      String             @id @default(uuid())
  templateId              String             // Pointing to compliance template.id
  label                   String             // e.g. 'Company Name', 'Company Address', 'Company Registration Number', 'Company Registration Date', 'Company Incorporation Date', 'Company Incorporation Number', 'Company Incorporation Date', 'Company Incorporation Number', 'Company Incorporation Date', 'Company Incorporation Number', 'Company Incorporation Date', 'Company Incorporation Number', 'Company Incorporation Date', 'Company Incorporation Number', 'Company Incorporation Date', 'Company Incorporation Number', 'Company Incorpor
  type                    String             // e.g. 'text', 'number', 'date', 'boolean', 'select', 'textarea', 'file', 'image', 'video', 'audio', 'signature'
  required                Boolean            // Is the rule required
  order                   Int                // Order of the rule within the compliance template e.g 1, 2, 3, 4, 5...
  config                  Json               // Config for the rule e.g. {min: 1, max: 100, options: ['Yes', 'No'],  maxFileSize: 1000000, minFileSize: 100000, maxFileCount: 10, minFileCount: 1, fileTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/tiff', 'image/webp', 'image/svg+xml', 'image/x-icon', 'image/x-portable-bitmap', 'image/x-portable-graymap', 'image/x-port}
  createdAt               DateTime           @default(now())
  updatedAt               DateTime           @updatedAt
  
  
  template                ComplianceTemplate @relation(fields: [templateId], references: [id])
}


model Addresses {                          // Represent an address
  id                      String           @id @default(uuid())
  houseNumber             String?          // e.g No 9 e.t.c
  street                  String           // e.g. 'Main Street'
  city                    String           // e.g. 'Ilorin'
  state                   String?          // e.g. 'Kwara' not all countries have states
  country                 String         
  postcode                Int?              
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt
  
  
  addressRelationships    AddressRelationship[]
}


model AddressRelationship {                // Link an address to a specific scope (i.e Business, BusinessGroup, User)
  id                      String           @id @default(uuid())
  type                    String           // e.g. Business_Office_Address or User_Home_Address
  addressId               String           // Pointing to address.id
  scopeId                 String           // Pointing to scope.id
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt


  address                 Addresses        @relation(fields: [addressId], references: [id])
  scope                   Scope            @relation(fields: [scopeId], references: [id])

  // Index for query optimization - quick lookups
  @@index([type])
  @@index([addressId])
  @@index([scopeId])
  @@index([addressId, scopeId])
}


model Document {                           // Represent a document
  id                      String           @id @default(uuid())
  title                   String           // e.g. 'CAC Registration Certificate'
  category                String           // e.g. 'CAC', 'Tax', 'Compliance', 'Reporting', 'Other' e.t.c  
  link                    String           // e.g. 'https://www.cac.gov.ng/registration-certificate'
  fileMimeType            String           // e.g. 'image', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'csv', 'json', 'xml', 'html', 'htm', 'zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'tgz', 'tbz2', 'txz', 'tlz', 'lzma', 'xz', 'lz', 'l
  fileSize                Int?             // e.g. 1000000 (1MB)
  source                  String           // 'External', 'System', 'User'
  filePath                String           // e.g. 'documents/cac-registration-certificate.pdf' 
  uploadedById            String?          // Pointing to user.id and optional if source is 'External'
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt
  
  
  uploadedBy              User?            @relation("UploadedBy", fields: [uploadedById], references: [id])

  @@index([category])
}


model Subscription {                       // Represent a subscription
  id                      String           @id @default(uuid())
  businessId              String           // Pointing to business.id
  plan                    String           // e.g. 'Starter', 'Growth', 'Pro', 'Enterprise'
  status                  String           // e.g. 'Active', 'Cancelled', 'Expired', 'Pending'
  startDate               DateTime         // Date subscription started
  endDate                 DateTime?        // Date subscription ends
  billingCycle            String           // e.g. 'Monthly', 'Quarterly', 'Annually', 'None'
  paymentMethod           String           // e.g. 'CreditCard', 'DebitCard', 'PayPal', 'BankTransfer', 'Cash', 'Cheque', 'System'
  amount                  Float            @default(0.00) // Amount paid for subscription
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt
  business                Business         @relation(fields: [businessId], references: [id])
}


model Note {                               // Represent a note - 
  id                      String           @id @default(uuid())
  businessId              String?          // Pointing to business.id - pointing to business incase a note belong to business
  userId                  String?          // Pointing to user.id - pointing to user incase a note belong to user
  content                 String           // Content of the note
  editable                Boolean          // Is the note editable 
  tags                    String[]         // Tags for the note
  format                  String           // Format of the note e.g. 'text', 'html', 'markdown'
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt
  
  business                Business?         @relation(fields: [businessId], references: [id])
  user                    User?             @relation(fields: [userId], references: [id])
}


model SecureVault {                        // Represent a secure vault
  id                      String           @id @default(uuid())
  businessId              String?           
  userId                  String?
  title                   String           // e.g. 'My Bank Account'
  credentials             Json             // e.g. {username: 'admin', password: 'admin'}
  lastModified            DateTime         @default(now())
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt
  

  business                Business?         @relation(fields: [businessId], references: [id])
  user                    User?             @relation(fields: [userId], references: [id])
}


model Integration {                        // Represent an integration
  id                      String           @id @default(uuid())
  businessId              String?          // Pointing to business.id of the business that the integration is linked to
  userId                  String?          // Pointing to user.id of the user that the integration is linked to
  name                    String           // e.g. 'Stripe', 'PayPal', 'Jira', 'Shopify', 'Slack', 'Wordpress', 'Shopify', e.t.c
  type                    String           // IncomingIntegration(i.e business's stripe account to ventureDirection) or OutgoingIntegration(i.e ventureDirection integration to a business's slack workspace)
  config                  Json             // Config for the integration e.g. {apiKey: '**********', secretKey: '**********', webhookUrl: 'https://www.example.com/webhook'} 
  status                  String           // e.g. 'Active', 'Inactive', 'Pending', 'Error'
  lastSync                DateTime?        // Last time the integration was synced
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt
  
  
  business                Business?        @relation(fields: [businessId], references: [id])
  user                    User?            @relation(fields: [userId], references: [id])
  installations           IntegrationInstallation[]
  
  @@index([name])
  @@index([businessId])
  @@index([userId])
}

model IntegrationInstallation {            // Represent an installation of an integration
  id                      String           @id @default(uuid())
  integrationId           String           // Pointing to integration.id of the integration that is installed 
  preferences             Json             // Preferences for the integration e.g. {channel: '#general', frequency: 'Daily'}
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt
  
  
  integration             Integration      @relation(fields: [integrationId], references: [id])
}
