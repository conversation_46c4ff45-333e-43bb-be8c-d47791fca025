# VentureDirection

VentureDirection is a business registration and management application.

The goal with the VentureDirection codebase is to build a Universal App that runs on Web, Mobile, and Desktop Targets. We should ever only need to write code once and it should work excellently well on every platform.

To make this happen, the codebase will be structured as a monorepo with a combined Next.js and React Native app. The client/frontend code will be written in React Native and the server/backend code will be written within Next.js.

This setup would support building apps for:
Web (Next.js)
Web (React Native Web - client-side only)
Mobile (iOS + Android - via React Native + Expo)
Desktop (macOS + Windows + Linux - via react-native-macos, react-native-windows, and Electron)

This app should also provide excellent performance and function as a PWA (offline support, service workers, etc). We combine Next, React Native, Expo Router, Solito, Tailwind, react-hook-form, zod, Tanstack Query, shadcn/React Native Reusables, and custom packages that we build and the React Compiler.



Folder structure:

```
package.json
app  <- combined NextJS and React Native app screens/pages - mainly routing
components <- React Native compatible components
packages <- any custom libraries built for shared/modular logic
lib <- any custom libraries code for shared/modular logic that is not enough to be an independent package
...
```

The components in the components folder are imported into and used in app/[locale]/**/(main|page|index).tsx files. These pages are also written using React Native.


## Getting Started

Follow these steps:

# Step 1: Clone the repository using the project's Git URL.
```sh
<NAME_EMAIL>:sparkstrand/venturedirection.git
```

# Step 2: Navigate to the project directory.
```sh
cd venturedirection
```

# Step 3: Install the necessary dependencies.

```bash
npm i
or
npm install
or
yarn install
or
yarn
```

# Step 4: Start the development server with auto-reloading and an instant preview.

```bash
npm run dev
or
yarn dev
```

Run the development server:

```bash
npm run web
# or
yarn web
```


```bash
npm run ios
# or
yarn ios
```

In the output, you'll find some options to open the app in a
- Open [http://localhost:3000](http://localhost:3000) with your browser to see the web version of the app (Next.js).
- Open [http://localhost:8081](http://localhost:8081) to see the mobile version of the app (React Native Web).
- [development build](https://docs.expo.dev/develop/development-builds/introduction/)
- [Android emulator](https://docs.expo.dev/workflow/android-studio-emulator/)
- [iOS simulator](https://docs.expo.dev/workflow/ios-simulator/)
- [Expo Go](https://expo.dev/go), a limited sandbox for trying out app development with Expo

You can start developing by editing the files inside the **app** directory. This project uses [file-based routing](https://docs.expo.dev/router/introduction).

## Routing:
The Next.js App Router is a file-system based router that uses React's latest features such as Server Components, Suspense, and Server Functions. The Expo Router is a file-based routing method for React Native, built on top of React Navigation.
These two routing approaches are somewhat similar.

The routes in the application are determined by the files in the app directory. For Next.js, a route is not publicly accessible until a page.tsx or route.ts file is added to a route segment, and only the content returned by page.tsx or route.ts is sent to the client. For Expo router, any file inside the app directory is a route, and the content returned by the file is sent to the client. In all cases, these routing files have a default export that defines a distinct page in the app (except for the special _layout.tsx/layout.tsx files).

All pages have a URL path that matches the file's location in the app directory, which can be used to navigate to that page in the address bar on the web, or as an app-specific deep link in a native mobile app.

To organise routes without affecting the URL, we can create a route group to keep related routes together. Accordingly, directories inside app define groups of related screens together as stacks, tabs, or in other arrangements. Route groups can be created by wrapping a folder in parenthesis: `(folderName)`. A directory with its name surrounded in parentheses indicates a route group. This indicates the folder is for organizational purposes and should not be included in the route's URL path. These directories are useful for grouping routes together without affecting the URL. The folders in parenthesis will be omitted from the URL (e.g. (marketing) or (shop)).

Even though routes inside (marketing) and (shop) share the same URL hierarchy, we can create a different layout for each group by adding a layout.tsx file inside their folders.

_layout.tsx files are special files that are not pages themselves but define how groups of routes inside a directory relate to each other. Layout routes are rendered before the actual page routes inside their directory. 
Each directory within the app directory (including app itself) can define a layout in the form of a _layout.tsx file inside that directory. This file defines how all the pages within that directory are arranged. This is where you would define a stack navigator, tab navigator, drawer navigator, or any other layout that you want to use for the pages in that directory. The layout file exports a default component that is rendered before whatever page you are navigating to within that directory.

## Routing Files
layout.tsx	Layout
_layout.tsx	Layout
page.tsx	Page
index.tsx	Page
loading.tsx	Loading UI
not-found.tsx	Not found UI
error.tsx	Error UI
global-error.tsx	Global error UI
route.ts	API endpoint
template.tsx	Re-rendered layout
default.tsx	Parallel route fallback page


## Routing File Precedence

We are introducing a new file naming convention to help with routing file precedence in this repository.

`main.tsx` would be the default file for both Next.js and React Native and the preferred file to use.

`page.tsx` would remain the default file for Next.js.

`index.tsx` would remain the default file for React Native.

### Basic concepts:

* `main.tsx` = generic "default" entry file (lowest priority)
* `page.tsx` = Next.js framework convention (framework-specific)
* `index.tsx` = React Native framework convention (framework-specific)
* Platform suffixes like `.web.tsx`, `.native.tsx`, `.electron.tsx`, `.android.tsx`, `.ios.tsx` increase specificity and override generic files.
* Electron behaves differently depending on the underlying framework (React Native or Next.js).


### File precedence per framework/platform:

| Platform             | Highest priority files (most specific)                                                                                                                                           |
| -------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Next.js Web**      | `page.web.tsx` > `page.tsx` > `index.web.tsx` > `index.tsx`                                                                                                                      |
| **React Native**     | `index.android.tsx` / `index.ios.tsx` > `index.native.tsx` > `index.tsx`                                                                                                         |
| **React Native Web** | `index.web.tsx` > `page.web.tsx` > `index.tsx`                                                                                                                                   |
| **Electron**         | - If built with Next.js: `page.electron.tsx` > `page.native.tsx` > `page.web.tsx` <br> - If built with React Native: `index.electron.tsx` > `index.native.tsx` > `index.web.tsx` |


### Combination cases:

| Files present                                      | Next.js picks   | React Native picks    | React Native Web picks                     | Electron picks                  |
| -------------------------------------------------- | --------------- | --------------------- | ------------------------------------------ | ------------------------------- |
| `main.tsx` only                                    | `main.tsx`      | `main.tsx`            | `main.tsx`                                 | `main.tsx`                      |
| `page.tsx` only                                    | `page.tsx`      | `page.tsx`            | `page.tsx`                                 | `page.tsx`                      |
| `index.tsx` only                                   | `index.tsx`     | `index.tsx`           | `index.tsx`                                | `index.tsx`                     |
| `main.tsx` + `page.tsx`                            | `page.tsx`      | `main.tsx`            | `main.tsx`                                 | depends on framework: see above |
| `main.tsx` + `index.tsx`                           | `main.tsx`      | `index.tsx`           | `index.tsx`                                | depends on framework            |
| `main.tsx` + `page.tsx` + `index.tsx`              | `page.tsx`      | `index.tsx`           | `index.tsx`                                | depends on framework            |
| `page.tsx` + `index.tsx`                           | `page.tsx`      | `index.tsx`           | `index.tsx`                                | depends on framework            |
| `index.tsx` + `index.web.tsx`                      | `index.web.tsx` | `index.tsx`           | `index.web.tsx`                            | depends on platform            |
| `index.tsx` + `index.web.tsx` + `index.native.tsx` | `index.web.tsx` | `index.native.tsx`    | `index.web.tsx`                            | depends on platform            |
| `index.web.tsx` + `page.tsx`                       | `index.web.tsx` | `page.tsx`            | `index.web.tsx`                            | depends on framework            |


### Electron specific note:

* **Electron picks files based on its underlying framework**:

  * **React Native Electron** uses the React Native conventions:

    * `index.electron.tsx` > `index.native.tsx` > `index.web.tsx` > `index.tsx` > `main.tsx`
  * **Next.js Electron** uses the Next.js conventions:

    * `page.electron.tsx` > `page.native.tsx` > `page.web.tsx` > `page.tsx` > `main.tsx`



# Example:

If you have these files in a folder:

```
main.tsx
page.tsx
index.tsx
index.web.tsx
index.native.tsx
index.android.tsx
index.ios.tsx
index.electron.tsx
page.electron.tsx
```

* On **Next.js Web**, it will pick `page.tsx` (most framework specific).
* On **React Native Android**, it picks `index.android.tsx`.
* On **React Native iOS**, it picks `index.ios.tsx`.
* On **React Native other native platforms** (e.g., Windows/macOS), it picks `index.native.tsx`.
* `main.tsx` and `page.tsx` are ignored on native platforms if more specific files exist.
* As both `index.electron.tsx` and `page.electron.tsx` both exist, it will depend on what the app running inside electron is built with. Most likely to be React Native and the `index.electron.tsx` will be used.



# File naming Summary

* Use the `main.tsx` generic fallback
* Think about the framework
  * Use `page.tsx` for Next.js / web-based routing.
  * Use `index.tsx` for React Native / native routing.
* Use platform suffixes to override files for specific targets (`.web.tsx`, `.native.tsx`, `.electron.tsx`, `.android.tsx`, `.ios.tsx`).
  * Web could mean Next.js or React Native Web
* Electron picks depend on underlying framework:
  * React Native Electron apps use `index.electron.tsx` first.
  * Next.js Electron apps use `page.electron.tsx` first.
  * Electron could be considered either Native or Web 
  * Election as Web could be either framework variant - Next.js or React Native Web



Expo Router will look for the first index.tsx file matching the / URL.

Inside app: 
- If there's a `layout.tsx` file, it will be used for all platforms
- If there's a `_layout.tsx` file, it will be used for React Native only
- If there's a `page.tsx` file, it will be used as the `index.tsx` for React Native
- If there's a `index.tsx` file, it will be used for React Native only

- It's required and must contain the <html> and <body> tags. The `_layout.tsx` file directly inside the app directory is rendered before any other route in the app. Virtually every app will have a _layout.tsx file directly inside the app directory. This is the root layout and represents the entry point for your navigation. In addition to describing the top-level navigator for your app, this file is where you would put initialization code, such as loading fonts, interacting with the splash screen, or adding context providers.





Routes starting with a + have special significance to Expo Router, and are used for specific purposes. One example is +not-found, which catches any requests that don't match a route in your app. +html is used to customize the HTML boilerplate used by your app on web. +native-intent is used to handle deep links into your app that don't match a specific route, such as links generated by third party services.

If you see square brackets in a file or directory name, you are looking at a dynamic route. The name of the route includes a parameter that can be used when rendering the page.











Dynamic routes	
[folder]	Dynamic route segment
[...folder]	Catch-all route segment
[[...folder]]	Optional catch-all route segment

Route Groups and private folders
(folder)	Group routes without affecting routing
_folder	Opt folder and all child segments out of routing

Parallel and Intercepted Routes
	
@folder	Named slot
(.)folder	Intercept same level
(..)folder	Intercept one level above
(..)(..)folder	Intercept two levels above
(...)folder	Intercept from root


Component hierarchy

The components defined in special files are rendered in a specific hierarchy:

    layout.tsx
    template.tsx
    error.tsx (React error boundary)
    loading.tsx (React suspense boundary)
    not-found.tsx (React error boundary)
    page.tsx or nested layout.tsx





Store all application code in shared folders in the root of your project and keeps the app directory purely for routing purposes.
Non-navigation components live outside of app directory. Things like components, hooks, utilities, and so on, should be placed in other top-level directories. Non-route files inside of the app directory, will be attemptted to be treated as a route.




# Navigation:
For navigation we will use Link from Solito (encompassing the both NextJS and Expo Router).

# Building:

When building a cross-platform app, you'll want to re-use as much code as possible. Scenarios may arise where it makes sense for the code to be different, for example you may want to implement separate visual components for Android and iOS.

React Native provides two ways to organize your code and separate it by platform:

    Using the Platform module.
    Using platform-specific file extensions.


React Native provides a module that detects the platform in which the app is running. You can use the detection logic to implement platform-specific code. Use this option when only small parts of a component are platform-specific.

When your platform-specific code is more complex, you should consider splitting the code out into separate files. React Native will detect when a file has a .ios. or .android. extension and load the relevant platform file when required from other components.

You can also use the .native.js extension when a module needs to be shared between NodeJS/Web and React Native but it has no Android/iOS differences.

For example, say you have the following files in your project:

```shell
Container.js # picked up by webpack, Rollup or any other Web bundler
Container.native.js # picked up by the React Native bundler for both Android and iOS (Metro)
```

You can still import it without the .native extension, as follows:
```tsx
import Container from './Container';
```

The Web bundler should be configured to ignore .native.js extensions in order to avoid having unused code in your production bundle, thus reducing the final bundle size.



If we create a public folder at the root of the project to store static assets such as images, fonts, etc. Files inside public can then be referenced by your code starting from the base URL (/).


Running the `npm build` will build the web app and output the files to the `web-build` directory.

Running the `npm run deploy` will deploy the web app to GitHub Pages.

Running `npm run ios` will open the iOS simulator.

Running `npm run android` will open the Android emulator.

The mobile commands will use Metro to bundle the app and start the app in the simulator.
The web commands will use Turbopack to bundle the app and start the app in the browser.


Usually libraries built specifically for other platforms will not work with React Native. Examples include react-select which is built for the web and specifically targets react-dom, and rimraf which is built for Node.js and interacts with your computer file system. Other libraries like lodash use only JavaScript language features and work in any environment. You will gain a sense for this over time, but until then the easiest way to find out is to try it yourself. You can remove packages using npm uninstall if it turns out that it does not work in React Native.






Anything we need to implement that does not have a platform agnostic equivalent, we'll potentially create a package for it within the repo.





At build time (and during hot-reload),
 - for NextJS:
react-native-web is used to transpile the React Native code to React DOM
The generated code is run through the React compiler
This is built using Turbopack
 - for mobile:
each page.tsx is converted to an into an index.jsx file for React Native by a metro transformer. If there is already an index.tsx in the folder, this will not be replaced.layout.tsx will become _layout.tsx. _layout is also added where none exist - for navigation.
React Native code is then compiled as-is
This is done using React compiler and Metro
You can code examples/inspiration in:
https://github.com/nandorojo/solito/tree/master/example-monorepos/with-tailwind
https://github.com/tamagui/starter-free/tree/main
https://github.com/nandorojo/solito/tree/master/example-monorepos/with-expo-router
https://solito.dev/guides/expo-router
CI/CD & Deployment:
Web: Deploy to Vercel
Mobile (iOS + Android): Expo Go for local dev, EAS Build otherwise, distribute via TestFlight (iOS) and Google Play Beta/Internal Track (Android)
Desktop (macOS, Windows, Linux): react-native-macos, react-native-windows, Electron wrapper (for unified experience), (electron-builder or expo-dev-client or react-native-builder-bob for packaging), sign for respective platforms, distribute via the website.
Client vs Server components:
Use Server-side Rendering (SSR):
- If the component is important for SEO (e.g., it contains content that should be indexed by search engines).
- If the initial load time matters and you’re concerned about the time it takes to render the page on the client-side (especially on slower devices). Server rendering, by sending pre-rendered HTML to the client, can reduce the time to first meaningful paint.
Use Static Site Generation (SSG) instead of SSR:
- If the component is entirely static (i.e., doesn’t depend on data or state that changes frequently). This would pre-render the component at build time and serve it as static HTML, instead of generating the HTML on every request. Next.js does this automatically when you use getStaticProps or getStaticPaths.
Client-Side Rendering (CSR), particularly useful when:
- If the component does not fetch any data and doesn't need to be SEO-friendly.
- If there's no need to involve the server for rendering.
- The component relies entirely on client-side JavaScript (e.g., user interactions).
- The component is relatively lightweight, so there’s no significant performance impact on the client.
Conclusion:
If the component doesn't fetch data, and doesn't need to be SEO-friendly or fast from the server, it’s often best to render it on the client-side for simplicity and performance. Use getStaticProps, getServerSideProps, or nothing accordingly.
Things like SSR (Server-Side Rendering) and SSG (Static Site Generation) apply only to the Web target. Mobile and desktop (React Native) are purely CSR (Client-Side Rendered). Use React Server Components in app/**/page.tsx files (i.e Next.js - export const dynamic = 'force-dynamic' // Enable SSR at the bottom of the file if running on Web). For SSG (e.g public marketing pages, blogs, or docs): export const dynamic = 'force-static'. Most other things should be CSR (Client-Side Rendering) - anything using client hooks (like useState, useEffect, react-query, zod, etc.)
Performance optimisations like this would be a use case for platform-aware components where needed.






## Application Entry Points

In React Native, the entry point file is the index.js. 
* This file is the first one that the React Native bundler loads when starting the app.
* index.js is the file where the React Native app registers the root component with the AppRegistry.
* App is the root component. We use the default export from the `/app` directory.
* AppRegistry.registerComponent tells React Native which component to run first.

In Next.js (app router), the entry point file is the `app/page.tsx` file. Though the root layout at `app/layout.tsx` is loaded first.

In Expo Router, the initial client file is `app/_layout.tsx`.


In each directory in the app folder, we should ever only have a core routing file (main.tsx, page.tsx, index.tsx) and optionally a layout file (_layout.tsx, layout.tsx).


## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/deployment) for more details.




## What technologies are used for this project?

This project is built with:

- Next.js
- Expo
- TypeScript
- React Native
- shadcn-ui
- Tailwind CSS


## Learn more

To learn more about developing your project with Expo, look at the following resources:

- [Expo documentation](https://docs.expo.dev/): Learn fundamentals, or go into advanced topics with our [guides](https://docs.expo.dev/guides).
- [Learn Expo tutorial](https://docs.expo.dev/tutorial/introduction/): Follow a step-by-step tutorial where you'll create a project that runs on Android, iOS, and the web.


