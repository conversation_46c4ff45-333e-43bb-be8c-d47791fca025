import { apiClient } from './client';
import { API_ENDPOINTS } from './endpoints';
import type { ApiResponse, SignIn } from '@/apps/web/library/types';

export class AuthApi {
  async GenerateSignInToken(data: SignIn): Promise<ApiResponse<string>> {
    return apiClient.post<ApiResponse<string>>(API_ENDPOINTS.SIGNINTOKEN, data)
  }

  async SignOut(userId: string): Promise<ApiResponse<null>> {
    return apiClient.post<ApiResponse<null>>(API_ENDPOINTS.SIGNOUT, userId)
  }

  /*this will call clerk auth to get the user's clerkUserId value*/
  async CheckAuth(): Promise<ApiResponse<any>> {
    return apiClient.post<ApiResponse<any>>(API_ENDPOINTS.CHECKAUTH)
  }

}

// Export singleton instance
export const authApi = new AuthApi();
