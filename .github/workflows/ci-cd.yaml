name: CI/CD Pipeline

on:
  pull_request:
    branches:
      - '**'
  push:
    branches:
      - main
      - '!dependabot/**'

env:
  NODE_ENV: CI
  DATABASE_URL: postgres://ventureDirectionUser:ventureDirectionPassword@localhost:5432/venturedirection

jobs:
  check-pr:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: lts/*
          cache: npm
      - name: Setup GitHub CLI
        uses: actions/cache@v4
        with:
          path: |
            /usr/bin/gh
            /usr/share/man/man1/gh.1.gz
          key: gh-cli-${{ runner.os }}-v4
      - run:
          name: Install GitHub CLI
          shell: bash
          run: |
            if ! command -v gh >/dev/null 2>&1; then
              sudo apt-get update && sudo apt-get install -y gh
            fi
      - uses: ./.github/actions/setup-pr-info

  run-tests:
    runs-on: ubuntu-latest
    needs: check-pr
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_DB: venturedirection
          POSTGRES_USER: ventureDirectionUser
          POSTGRES_PASSWORD: ventureDirectionPassword
        options: >-
          --health-cmd "pg_isready -U ventureDirectionUser -d venturedirection"
          --health-interval 5s
          --health-timeout 5s
          --health-retries 10
        ports:
          - 5432:5432
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: lts/*
          cache: npm
      - uses: ./.github/actions/setup-pr-info
      - name: Setup and Seed Database with Prisma
        run: |
          npx prisma generate
          npx prisma migrate deploy
          npm run prisma:seed
      - name: Run Unit Tests
        run: npm test 2> unit_test_failure.txt
      - name: Notify on Failure
        if: failure()
        env:
          FAILURE_WEBHOOK_URL: ${{ secrets.FAILURE_WEBHOOK_URL }}
        run: |
          failure_reason=$(cat unit_test_failure.txt || echo "Unknown error")
          payload=$(jq -n \
            --arg username "$LAST_COMMITTER_GITHUB" \
            --arg message "failed-pipeline" \
            --arg pr "${{ github.event.pull_request.html_url }}" \
            --arg pr_number "$PR_NUMBER" \
            --arg pr_title "$PR_TITLE" \
            --arg committer "$LAST_COMMITTER_NAME" \
            --arg reason "$failure_reason" \
            --arg branch "$GITHUB_REF_NAME" \
            --arg repo "$GITHUB_REPOSITORY" \
            --arg sha "$GITHUB_SHA" \
            '{username: $username, message: $message, vars: {CIRCLE_PULL_REQUEST: $pr, PR_NUMBER: $pr_number, PR_TITLE: $pr_title, LAST_COMMITTER_NAME: $committer, LAST_COMMITTER_GITHUB: $username, CIRCLE_BRANCH: $branch, CIRCLE_PROJECT_REPONAME: "${{ github.event.repository.name }}", CIRCLE_PROJECT_USERNAME: "${{ github.repository_owner }}", CIRCLE_REPOSITORY_URL: "${{ github.event.repository.html_url }}", CIRCLE_SHA1: $sha, failure_reason: $reason}}')
          echo "Sending failure notification: $payload"
          if [ -n "$FAILURE_WEBHOOK_URL" ]; then
            curl -X POST -H 'Content-Type: application/json' --data "$payload" "$FAILURE_WEBHOOK_URL"
          else
            echo "No failure webhook URL configured."
          fi

  preview-deployment:
    runs-on: ubuntu-latest
    needs: run-tests
    if: github.event_name == 'pull_request' && !startsWith(github.head_ref, 'dependabot/')
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: lts/*
          cache: npm
      - uses: ./.github/actions/setup-pr-info
      - name: Cache Vercel CLI
        uses: actions/cache@v4
        with:
          path: |
            /usr/local/lib/node_modules/vercel
            /usr/local/bin/vercel
          key: vercel-cli-${{ runner.os }}-v2
      - name: Install Vercel CLI
        run: |
          if ! command -v vercel >/dev/null 2>&1; then
            npm install -g vercel@latest
          fi
      - name: Pull Vercel Environment
        run: npx vercel pull --yes --environment=preview --token=${{ secrets.VERCEL_TOKEN }}
      - name: Load Vercel Env Vars
        run: source .vercel/.env.preview.local
      - name: Build Preview
        run: npx vercel build --token=${{ secrets.VERCEL_TOKEN }} 2> build_failure.txt
      - name: Notify on Failure
        if: failure()
        env:
          FAILURE_WEBHOOK_URL: ${{ secrets.FAILURE_WEBHOOK_URL }}
        run: |
          failure_reason=$(cat build_failure.txt || echo "Unknown error")
          payload=$(jq -n \
            --arg username "$LAST_COMMITTER_GITHUB" \
            --arg message "failed-pipeline" \
            --arg pr "${{ github.event.pull_request.html_url }}" \
            --arg pr_number "$PR_NUMBER" \
            --arg pr_title "$PR_TITLE" \
            --arg committer "$LAST_COMMITTER_NAME" \
            --arg reason "$failure_reason" \
            --arg branch "$GITHUB_REF_NAME" \
            --arg repo "$GITHUB_REPOSITORY" \
            --arg sha "$GITHUB_SHA" \
            '{username: $username, message: $message, vars: {CIRCLE_PULL_REQUEST: $pr, PR_NUMBER: $pr_number, PR_TITLE: $pr_title, LAST_COMMITTER_NAME: $committer, LAST_COMMITTER_GITHUB: $username, CIRCLE_BRANCH: $branch, CIRCLE_PROJECT_REPONAME: "${{ github.event.repository.name }}", CIRCLE_PROJECT_USERNAME: "${{ github.repository_owner }}", CIRCLE_REPOSITORY_URL: "${{ github.event.repository.html_url }}", CIRCLE_SHA1: $sha, failure_reason: $reason}}')
          echo "Sending failure notification: $payload"
          if [ -n "$FAILURE_WEBHOOK_URL" ]; then
            curl -X POST -H 'Content-Type: application/json' --data "$payload" "$FAILURE_WEBHOOK_URL"
          else
            echo "No failure webhook URL configured."
          fi
      - name: Deploy Preview
        id: deploy
        run: |
          preview_url=$(npx vercel deploy --prebuilt --token=${{ secrets.VERCEL_TOKEN }} --archive=tgz)
          echo "PREVIEW_URL=$preview_url" >> $GITHUB_ENV
      - name: Notify Preview Deployment
        env:
          CIRCLE_PULL_REQUEST: ${{ github.event.pull_request.html_url }}
        run: |
          PR_NUMBER=${{ github.event.pull_request.number || '0' }}
          COMMIT_AUTHOR=$(gh api /repos/${{ github.repository }}/commits/${{ github.sha }} --jq '.author.login')
          JSON_PAYLOAD=$(jq -n \
            --arg pr_number "$PR_NUMBER" \
            --arg preview_url "$PREVIEW_URL" \
            --arg branch_name "$GITHUB_REF_NAME" \
            --arg repo_name "${{ github.event.repository.name }}" \
            --arg repo_owner "${{ github.repository_owner }}" \
            --arg author "$COMMIT_AUTHOR" \
            '{pr_number: $pr_number, preview_url: $preview_url, branch_name: $branch_name, repo_name: $repo_name, repo_owner: $repo_owner, author: $author}')
          echo "Preview Deployment Payload: $JSON_PAYLOAD"
          if [ "$PR_NUMBER" != "0" ]; then
            curl -X POST https://sparkstrand-github-app.onrender.com/deployment/notification -H "Content-Type: application/json" -d "$JSON_PAYLOAD"
          fi

  production-deployment:
    runs-on: ubuntu-latest
    needs: run-tests
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v4
      - name: Skip Dependabot Commits
        run: |
          AUTHOR=$(git log -1 --pretty=format:'%ae')
          if [[ "$AUTHOR" == "dependabot[bot]@users.noreply.github.com" ]]; then
            echo "Skipping production deployment for Dependabot"
            exit 0
          fi
      - uses: actions/setup-node@v4
        with:
          node-version: lts/*
          cache: npm
      - name: Cache Vercel CLI
        uses: actions/cache@v4
        with:
          path: |
            /usr/local/lib/node_modules/vercel
            /usr/local/bin/vercel
          key: vercel-cli-${{ runner.os }}-v2
      - name: Install Vercel CLI
        run: |
          if ! command -v vercel >/dev/null 2>&1; then
            npm install -g vercel@latest
          fi
      - name: Pull Vercel Production Env
        run: npx vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}
      - name: Load Vercel Env Vars
        run: source .vercel/.env.production.local
      - name: Build Production
        run: npx vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}
      - name: Deploy Production
        run: |
          production_url=$(npx vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }} --archive=tgz)
          echo "Production deployment: $production_url"