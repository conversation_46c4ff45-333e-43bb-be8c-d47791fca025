
import { Inter } from 'next/font/google'
import '../globals.css'
// import { ThemeProvider } from '@/components/theme-provider';
import { QueryProvider } from '@/providers/query-provider';
// import { BusinessProvider } from '@/lib/contexts/business-context'; 
// import { Toaster } from '@/components/common/sonner';
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { routing } from '../../i18n/routing';

// Initialize the font for web
const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter'
});

export const metadata = {
  title: 'VentureDirection - Business Management Platform',
  description: 'Comprehensive business management and compliance platform',
}

export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }))
}

export default async function LocaleLayout({
  children,
  params
}: {
  children: React.ReactNode
  params: Promise<{ locale: string }>
}) {
  // Await the params object
  const { locale } = await params

  // Ensure that the incoming `locale` is valid
  if (!routing.locales.includes(locale as any)) {
    notFound()
  }

  // Providing all messages to the client side
  const messages = await getMessages({ locale })

  return (
    <html lang={locale} suppressHydrationWarning>
      <body className={`${inter.variable} font-sans`}>
        <NextIntlClientProvider messages={messages}>
          {/* <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          > */}
            <QueryProvider>
              {/* <BusinessProvider> */}
                {children}
                {/* <Toaster />
              </BusinessProvider> */}
            </QueryProvider>
          {/* </ThemeProvider> */}
        </NextIntlClientProvider>
      </body>
    </html>
  )
}
