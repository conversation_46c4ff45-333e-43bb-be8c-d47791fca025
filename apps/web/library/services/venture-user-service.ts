import { prisma } from '@/apps/web/db'
import { auth } from '@clerk/nextjs/server'
import type { 
  VentureUser, 
  VentureUserRole, 
  VentureUserPermission,
  VentureUserBusinessContext,
  ResolveUserOptions,
  CreateVentureUserData,
} from '@/apps/web/library/types/venture-user'


export class VentureUserService {
  
  /**
   * Get VentureUser by Clerk user ID with full Business context
   */
  async getVentureUserByClerkId(options: ResolveUserOptions = {}): Promise<VentureUser | null> {
    const { userId: clerkUserId } = await auth();
    if(!clerkUserId) return null;

    const user = await prisma.user.findUnique({
      where: { clerkUserId: clerkUserId as string },
      include: {
        businessMemberships: {
          where: { isActive: true },
          include: {  
            business: {
              include: {
                businessWithGroupRelationships: {
                  include: {
                    businessGroup: true
                  }
                }
              }
            },
            businessGroup: true
          }
        },
        roleAssignments: options.includeRoles ? {
          where: { 
            isActive: true,
            revokedAt: null
          },
          include: {
            role: {
              include: {
                rolePermissions: {
                  include: {
                    permission: true
                  }
                }
              }
            },
            scope: true
          }
        } : false
      }
    })

    if (!user) return null

    return this.transformToVentureUser(user, options)
  }

  // /**
  //  * Get VentureUser by WorkOS user ID - for enterprise
  //  */
  // async getVentureUserByWorkOSId(
  //   workosUserId: string,
  //   options: ResolveUserOptions = {}
  // ): Promise<VentureUser | null> {
  //   const user = await prisma.user.findUnique({
  //     where: { workosUserId },
  //     include: {
  //       businessMemberships: {
  //         where: { isActive: true },
  //         include: {
  //           business: {
  //             include: {
  //               businessWithGroupRelationships: {
  //                 include: {
  //                   businessGroup: true
  //                 }
  //               }
  //             }
  //           },
  //           businessGroup: true
  //         }
  //       },
  //       roleAssignments: options.includeRoles ? {
  //         where: { 
  //           isActive: true,
  //           revokedAt: null
  //         },
  //         include: {
  //           role: {
  //             include: {
  //               permissions: {
  //                 include: {
  //                   permission: true
  //                 }
  //               }
  //             }
  //           },
  //           scope: true
  //         }
  //       } : false
  //     }
  //   })

  //   if (!user) return null

  //   return this.transformToVentureUser(user, options)
  // }

  /**
   * Get current authenticated VentureUser with session context
   */
  async getCurrentVentureUser(options: ResolveUserOptions = {}): Promise<VentureUser | null> {
    
    const { userId: clerkUserId } = await auth()
    
    if (!clerkUserId) return null

    const user = await prisma.user.findUnique({
      where: { clerkUserId: clerkUserId as string },
    })

    if (!user) return null

    // Get metadata for business context - this will redis 
    const currentBusiness = await prisma.userBusinessContext.findUnique({
      where: { userId: user.id  as string},
    })

    const ventureUser = await this.getVentureUserByClerkId({
      ...options,
      businessId: options.businessId || currentBusiness?.activeBusinessId,
      businessGroupId: options.businessGroupId || currentBusiness?.activeBusinessGroupId
    })

    return ventureUser 
  }

  /**
   * Create or update VentureUser from auth provider data
   */
  async upsertVentureUser(data: CreateVentureUserData): Promise<VentureUser> {
    const whereClause = { clerkUserId: data.clerkUserId } ; //data.clerkUserId 
      // ? { clerkUserId: data.clerkUserId }
      // : { workosUserId: data.workosUserId }

    const user = await prisma.user.upsert({
      where: whereClause,
      update: data,
      create: data as any,
      include: {
        businessMemberships: {
          where: { isActive: true },
          include: {
            business: {
              include: {
                businessWithGroupRelationships: {
                  include: {
                    businessGroup: true
                  }
                }
              }
            }
          }
        }
      }
    })

    return this.transformToVentureUser(user, { includeRoles: true, includePermissions: true })
  }

  /**
   * Check if user has specific permission
   */
  async hasPermission(
    userId: string, 
    permission: string, 
    businessId?: string
  ): Promise<boolean> {
    const roleAssignments = await prisma.roleAssignment.findMany({
      where: {
        userId,
        isActive: true,
        revokedAt: null,
        ...(businessId && {
          scope: {
            type: 'BUSINESS',
            entityId: businessId
          }
        })
      },
      include: {
        role: {
          include: {
            rolePermissions: {
              include: {
                permission: true
              }
            }
          }
        }
      }
    })

    return roleAssignments.some((assignment: any) =>
      assignment.role.rolePermissions.some((rp: any) => 
        rp.permission.name === permission
      )
    )
  }

  /**
   * Check if user has specific role
   */
  async hasRole(
    userId: string, 
    roleTitle: string, 
    businessId?: string
  ): Promise<boolean> {
    const roleAssignment = await prisma.roleAssignment.findFirst({
      where: {
        userId,
        isActive: true,
        revokedAt: null,
        role: {
          title: roleTitle
        },
        ...(businessId && {
          scope: {
            type: 'BUSINESS',
            entityId: businessId
          }
        })
      }
    })

    return !!roleAssignment
  }

  /**
   * Transform Prisma user to VentureUser
   */
  private transformToVentureUser(user: any, options: ResolveUserOptions): VentureUser {
    // Build business memberships
    const businessMemberships: VentureUserBusinessContext[] = user.businessMemberships?.map((membership: any) => ({
      businessId: membership.business.id,
      businessName: membership.business.name,
      businessType: membership.business.type,
      businessGroupId: membership.businessGroupId,
      businessGroupName: membership.businessGroup?.name,
      userRole: membership.roleTitle,
      permissions: [], // Will be populated if includePermissions is true
      isActive: membership.isActive,
      lastAccessedAt: membership.lastAccessedAt
    })) || []

    // Build roles for current context
    const currentRoles: VentureUserRole[] = []
    const currentPermissions: VentureUserPermission[] = []
    const globalRoles: VentureUserRole[] = []

    if (user.roleAssignments) {
      user.roleAssignments.forEach((assignment: any) => {
        const role: VentureUserRole = {
          id: assignment.role.id,
          title: assignment.role.title,
          description: assignment.role.description,
          permissions: assignment.role.permissions?.map((rp: any) => rp.permission.name) || [],
          scope: {
            type: assignment.scope.type,
            entityId: assignment.scope.entityId,
            entityName: assignment.scope.entityName
          },
          isActive: assignment.isActive,
          assignedAt: assignment.assignedAt
        }

        if (assignment.scope.type === 'GLOBAL') {
          globalRoles.push(role)
        } else if (
          options.businessId && 
          assignment.scope.entityId === options.businessId
        ) {
          currentRoles.push(role)
          
          // Add permissions for current business context
          assignment.role.permissions?.forEach((rp: any) => {
            currentPermissions.push({
              id: rp.permission.id,
              name: rp.permission.name,
              description: rp.permission.description,
              resource: rp.permission.resource,
              action: rp.permission.action,
              scope: {
                type: assignment.scope.type,
                entityId: assignment.scope.entityId
              }
            })
          })
        }
      })
    }

    // Compute helper properties
    const hasBusinessAccess = businessMemberships.length > 0
    const isBusinessOwner = currentRoles.some(role => role.title.toLowerCase().includes('owner'))
    const isBusinessAdmin = currentRoles.some(role => 
      role.title.toLowerCase().includes('admin') || 
      role.title.toLowerCase().includes('owner')
    )
    const canManageUsers = currentPermissions.some(p => 
      p.resource === 'users' && p.action === 'manage'
    )
    const canManageSettings = currentPermissions.some(p => 
      p.resource === 'settings' && p.action === 'manage'
    )

    return {
      id: user.id,
      clerkUserId: user.clerkUserId,
      workosUserId: user.workosUserId,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      displayName: user.firstName && user.lastName 
        ? `${user.firstName} ${user.lastName}`
        : user.email,
      avatar: user.avatar,
      isActive: user.isActive,
      emailVerified: user.emailVerified,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      lastLoginAt: user.lastLoginAt,
      currentBusinessId: options.businessId,
      currentBusinessGroupId: options.businessGroupId,
      businessMemberships,
      currentRoles,
      currentPermissions,
      globalRoles,
      hasBusinessAccess,
      isBusinessOwner,
      isBusinessAdmin,
      canManageUsers,
      canManageSettings
    }
  }
}

export const ventureUserService = new VentureUserService()

/**
 * Server-side helper to get current VentureUser
 */
export async function getVentureUser(options: ResolveUserOptions = {}): Promise<VentureUser | null> {
  return ventureUserService.getCurrentVentureUser(options)
}
