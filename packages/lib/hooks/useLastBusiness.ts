// import { useEffect, useState } from 'react'
// import { useUser } from '@clerk/nextjs'

// interface LastBusinessInfo {
//   lastBusinessId: string | null
//   isLoading: boolean
//   error: string | null
// }

// /**
//  * Hook to get user's last accessed business for smart defaults
//  */
// export function useLastBusiness(): LastBusinessInfo {
//   const { user, isLoaded } = useUser()
//   const [lastBusinessId, setLastBusinessId] = useState<string | null>(null)
//   const [isLoading, setIsLoading] = useState(true)
//   const [error, setError] = useState<string | null>(null)

//   useEffect(() => {
//     async function fetchLastBusiness() {
//       if (!isLoaded || !user?.id) {
//         setIsLoading(false)
//         return
//       }

//       try {
//         setIsLoading(true)
//         setError(null)

//         const response = await fetch('/api/default-business')
//         if (!response.ok) {
//           throw new Error('Failed to fetch last business')
//         }

//         const data = await response.json()
//         setLastBusinessId(data.defaultBusinessId)
//       } catch (err) {
//         console.error('Error fetching last business:', err)
//         setError(err instanceof Error ? err.message : 'Unknown error')
//       } finally {
//         setIsLoading(false)
//       }
//     }

//     fetchLastBusiness()
//   }, [isLoaded, user?.id])

//   return {
//     lastBusinessId,
//     isLoading,
//     error,
//   }
// }

// /**
//  * Hook to initialize user context with smart business selection
//  */
// export function useInitializeBusiness() {
//   const { user, isLoaded } = useUser()

//   const initializeContext = async (): Promise<boolean> => {
//     if (!isLoaded || !user?.id) {
//       return false
//     }

//     try {
//       const response = await fetch('/api/default-business', {
//         method: 'POST'
//       })

//       if (!response.ok) {
//         throw new Error('Failed to initialize business context')
//       }

//       const data = await response.json()
//       return data.success
//     } catch (err) {
//       console.error('Error initializing business context:', err)
//       return false
//     }
//   }

//   return { initializeContext }
// }
