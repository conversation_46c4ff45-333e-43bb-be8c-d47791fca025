{"name": "venturedirection", "version": "0.1.0", "private": true, "main": "expo-router/entry", "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "clean": "turbo run clean && rm -rf node_modules yarn.lock .turbo", "build": "turbo run build", "start": "turbo run start", "lint": "turbo run lint", "native:dev": "cd apps/native && yarn dev", "native:clean": "cd apps/native && yarn clean", "native:ios": "cd apps/native && yarn ios", "native:android": "cd apps/native && yarn android", "native:web": "cd apps/native && yarn web", "web:dev": "cd apps/web && yarn dev", "web:build": "cd apps/web && yarn build", "web:start": "cd apps/web && yarn start", "web:lint": "cd apps/web && yarn lint", "web:clean": "cd apps/web && yarn clean", "reset": "yarn clean && yarn install && yarn prisma:generate", "typecheck": "turbo run typecheck", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json}\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --selectProjects unit", "test:integration": "jest --selectProjects integration", "test:component": "jest --selectProjects component", "test:api": "cd apps/web && jest --config jest.config.js --testPathPatterns=__tests__/api", "test:services": "cd apps/web && jest --config jest.config.js --testPathPatterns=library/services/__tests__", "test:packages": "jest --testPathPatterns=packages", "test:lib": "jest --testPathPatterns=packages/lib", "test:ui": "jest --testPathPatterns=packages/ui", "test:app": "jest --testPathPatterns=packages/app", "test:ci": "jest --ci --coverage --watchAll=false", "test:api:watch": "cd apps/web && jest --config jest.config.js --testPathPatterns=__tests__/api --watch", "test:services:watch": "cd apps/web && jest --config jest.config.js --testPathPatterns=library/services/__tests__ --watch", "test:runner": "node scripts/test-runner.js", "prisma:push": "(cd apps/web && yarn prisma db push)", "prisma:seed": "(cd apps/web && yarn prisma db seed)", "prisma:studio": "(cd apps/web && yarn prisma studio)", "prisma:generate": "(cd apps/web && yarn prisma generate)", "prisma:migrate": "(cd apps/web && yarn prisma migrate deploy)", "docker:build": "docker build -t venture-direction ."}, "dependencies": {"@tanstack/react-query": "^5.81.2", "axios": "^1.10.0", "clsx": "^2.1.1", "nativewind": "^4.1.23", "postcss": "^8", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.67"}, "resolutions": {"react": "19.0.0", "react-dom": "19.0.0", "react-native": "^0.79.2", "react-native-web": "~0.20.0", "tailwindcss": "^3.4.17"}, "devDependencies": {"@babel/core": "^7.27.4", "@eslint/eslintrc": "^3", "@eslint/js": "^9.29.0", "@tailwindcss/postcss": "^4", "@tanstack/react-query-devtools": "^5.81.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/jest-native": "^5.4.3", "@testing-library/react": "^16.3.0", "@testing-library/react-native": "^13.2.0", "@types/jest": "^30.0.0", "@types/node": "^24", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "@types/react-test-renderer": "19.0.0", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-expo": "~9.2.0", "eslint-config-next": "15.3.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "jest": "^30.0.3", "jest-environment-jsdom": "^30.0.2", "lefthook": "^1.11.14", "prettier": "^3.6.1", "react-test-renderer": "19.0.0", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "turbo": "^2.5.4", "typescript": "5.8.3", "typescript-eslint": "^8.35.0"}, "packageManager": "yarn@4.9.2"}