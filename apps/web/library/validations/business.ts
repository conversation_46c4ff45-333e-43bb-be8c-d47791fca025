import { z } from 'zod'
import { BusinessRegistrationStatus, BusinessSystemStatus, BusinessStatus } from '@prisma/client'

export const createBusinessSchema = z.object({
  name: z.string().min(1, 'Business name is required').max(255, 'Business name is too long'),
  type: z.string().optional(),
  countryId: z.string().uuid('Invalid country ID'),
  registrationStatus: z.nativeEnum(BusinessRegistrationStatus).optional(),
  registrationNumber: z.string().optional(),
  incorporationDate: z.coerce.date().optional(),
  settings: z.record(z.any()).optional(),
  industryIds: z.array(z.string().uuid()).optional(),
})

export const updateBusinessSchema = z.object({
  name: z.string().min(1, 'Business name is required').max(255, 'Business name is too long').optional(),
  type: z.string().optional(),
  countryId: z.string().uuid('Invalid country ID').optional(),
  registrationStatus: z.nativeEnum(BusinessRegistrationStatus).optional(),
  systemStatus: z.nativeEnum(BusinessSystemStatus).optional(),
  businessStatus: z.nativeEnum(BusinessStatus).optional(),
  registrationNumber: z.string().optional(),
  incorporationDate: z.coerce.date().optional(),
  settings: z.record(z.any()).optional(),
})

export const businessFiltersSchema = z.object({
  search: z.string().optional(),
  countryId: z.string().uuid().optional(),
  registrationStatus: z.nativeEnum(BusinessRegistrationStatus).optional(),
  systemStatus: z.nativeEnum(BusinessSystemStatus).optional(),
  businessStatus: z.nativeEnum(BusinessStatus).optional(),
  industryId: z.string().uuid().optional(),
  sortBy: z.enum(['name', 'createdAt', 'updatedAt']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
  page: z.coerce.number().min(1).optional(),
  limit: z.coerce.number().min(1).max(100).optional(),
})

export type CreateBusinessData = z.infer<typeof createBusinessSchema>
export type UpdateBusinessData = z.infer<typeof updateBusinessSchema>
export type BusinessFiltersData = z.infer<typeof businessFiltersSchema>
