# VentureDirection Folder Structure

## 📁 New Organized Route Structure

### Overview

The VentureDirection platform uses a clean, organized folder structure that separates business features from general dashboard functionality. This makes the codebase more maintainable and the middleware simpler.

## 🏗️ Route Organization

### 1. **Business Routes (`/business/*`)**

All business-specific features are now organized under the `/business` folder:

```
app/[locale]/business/
├── page.tsx                     # Business dashboard (main business view)
├── onboard/page.tsx             # Business onboarding guide
├── store/page.tsx               # E-commerce management
├── orders/page.tsx              # Order fulfillment
├── inventory/page.tsx           # Stock management
├── crm/
│   ├── page.tsx                 # Customer relationships
│   └── [customerId]/view/page.tsx # Customer details
├── finance/
│   ├── page.tsx                 # Financial management
│   └── invoices/[invoiceId]/edit/page.tsx # Invoice editor
├── compliance/page.tsx          # Regulatory compliance
├── documents/page.tsx           # Document management
├── secure-vault/page.tsx        # Credentials manager
├── planner/page.tsx             # Notes and task board
├── hr/page.tsx                  # Human resources
├── roles/page.tsx               # Access control
├── marketing/page.tsx           # Marketing campaigns
├── analytics/page.tsx           # Business intelligence
├── integrations/page.tsx        # Stripe, Paystack, WhatsApp
├── logistics/page.tsx           # Shipment plugins (future)
└── settings/page.tsx            # Business configuration
```

### 2. **Business Group Routes (`/business-group/*`)**

Multi-business and franchise management features:

```
app/[locale]/business-group/
├── page.tsx                     # Business group dashboard
├── overview/page.tsx            # Group overview and metrics
├── businesses/
│   ├── page.tsx                 # Manage businesses in group
│   └── [businessId]/page.tsx    # Individual business management
├── members/page.tsx             # Group member management
├── roles/page.tsx               # Group-level role management
├── settings/page.tsx            # Group configuration
├── billing/page.tsx             # Group billing and subscriptions
└── analytics/page.tsx           # Cross-business analytics
```

### 3. **General Dashboard Routes (`/dashboard`)**

Non-business-specific functionality:

```
app/[locale]/dashboard/
├── page.tsx                     # Main dashboard (business selection)
├── select-business/page.tsx     # Business selection interface
└── businesses/page.tsx          # List all businesses
├── create/page.tsx              # Create new business
```

## 🔧 Middleware Configuration

### Route Matching

The middleware now uses clean, simple route matchers:

```typescript
// Business routes - require authentication + active business
const isBusinessRoute = createRouteMatcher([
  '/business(.*)',
  '/(en|fr|es|de|ie|ng|ee)/business(.*)',
])

// Business group routes - require authentication + active business group
const isBusinessGroupRoute = createRouteMatcher([
  '/business-group(.*)',
  '/(en|fr|es|de|ie|ng|ee)/business-group(.*)',
])

// Protected routes - require authentication only
const isProtectedRoute = createRouteMatcher([
  '/dashboard(.*)',
  '/select-business(.*)',
  '/(en|fr|es|de|ie|ng|ee)/dashboard(.*)',
  '/(en|fr|es|de|ie|ng|ee)/select-business(.*)',
])
```

### Context Requirements

- **`/business/*`** - Requires active business context (`activeBusinessId`)
- **`/business-group/*`** - Requires active business group context (`activeBusinessGroupId`)
- **`/dashboard/*`** - General authentication only

## 🎯 Benefits of New Structure

### 1. **Cleaner Middleware**
- No need to list every individual route
- Simple pattern matching with `(.*)`
- Clear separation of concerns

### 2. **Better Organization**
- Related features grouped together
- Easier to find and maintain code
- Logical folder hierarchy

### 3. **Scalability**
- Easy to add new business features
- Clear patterns for new developers
- Consistent structure across features

### 4. **Context Clarity**
- Clear which routes need business context
- Obvious business group vs business distinction
- Simplified permission management

## 🔄 Migration Guide



### URL format

```typescript

/dashboard          # General dashboard (business selection)
/business           # Business dashboard
/business/store     # Business feature
/business/orders    # Business feature
/business/finance   # Business feature
/business-group     # Business group dashboard
/dashboard/create/business   # Create new business
/dashboard/create/business-group  # Create new business group
```

## 🏗️ Layout Integration

### Business Layout

All `/business/*` routes use the `BusinessWorkspaceLayout`:

```typescript
// app/[locale]/business/layout.tsx
export default function BusinessLayout({ children }) {
  const { currentBusiness } = useBusiness()
  
  return (
    <BusinessWorkspaceLayout businessId={currentBusiness?.id}>
      {children}
    </BusinessWorkspaceLayout>
  )
}
```

### Business Group Layout

All `/business-group/*` routes use a dedicated business group layout:

```typescript
// app/[locale]/business-group/layout.tsx
export default function BusinessGroupLayout({ children }) {
  const { currentBusinessGroup } = useBusinessGroup()
  
  return (
    <BusinessGroupLayout businessGroupId={currentBusinessGroup?.id}>
      {children}
    </BusinessGroupLayout>
  )
}
```

## 🔒 Permission Scoping

### Automatic Context

- **Business routes** automatically have business context
- **Business group routes** automatically have business group context
- **No manual business ID passing** required

### Permission Checks

```typescript
// In /business/* routes
const { canManageOrders } = useBusinessUser() // Auto-scoped to current business

// In /business-group/* routes  
const { canManageBusinesses } = useBusinessGroupUser() // Auto-scoped to current group
```

## 📱 Navigation Updates

### Business Navigation

```typescript
// Business-specific navigation
const businessNavigation = [
  { name: 'Dashboard', href: '/business' },
  { name: 'Store', href: '/business/store' },
  { name: 'Orders', href: '/business/orders' },
  { name: 'Finance', href: '/business/finance' },
  // ...
]
```

### Business Group Navigation

```typescript
// Business group navigation
const businessGroupNavigation = [
  { name: 'Overview', href: '/business-group' },
  { name: 'Businesses', href: '/business-group/businesses' },
  { name: 'Members', href: '/business-group/members' },
  { name: 'Settings', href: '/business-group/settings' },
  // ...
]
```

## 🎯 Future Extensibility

### Adding New Business Features

```typescript
// Simply add to /business/ folder
app/[locale]/business/
└── new-feature/
    ├── page.tsx
    └── [id]/page.tsx
```

### Adding New Business Group Features

```typescript
// Simply add to /business-group/ folder
app/[locale]/business-group/
└── new-feature/
    ├── page.tsx
    └── [id]/page.tsx
```

### No Middleware Changes Needed

The middleware automatically protects any route under:
- `/business/*` (requires business context)
- `/business-group/*` (requires business group context)
- `/dashboard/*`  (requires authentication)

## 🚀 Implementation Benefits

1. **Simplified Middleware** - Clean pattern matching
2. **Better Organization** - Logical feature grouping
3. **Automatic Context** - No manual context passing
4. **Scalable Architecture** - Easy to extend
5. **Clear Permissions** - Obvious scope boundaries
6. **Developer Experience** - Intuitive folder structure
