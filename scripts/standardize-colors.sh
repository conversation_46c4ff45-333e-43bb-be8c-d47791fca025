#!/bin/bash

# VentureDirection Color Standardization Script
# Automatically updates components to use venture-* color classes

echo "🎨 Starting VentureDirection color standardization..."

# Create backup directory
BACKUP_DIR="backup-$(date +%Y%m%d-%H%M%S)"
echo "📁 Creating backup in $BACKUP_DIR..."
mkdir -p "$BACKUP_DIR"
cp -r components/ "$BACKUP_DIR/"

# Function to update files
update_colors() {
    local file="$1"
    echo "  Updating: $file"
    
    # Replace common blue color patterns with venture colors
    sed -i.bak \
        -e 's/bg-blue-50/bg-venture-50/g' \
        -e 's/bg-blue-100/bg-venture-100/g' \
        -e 's/bg-blue-200/bg-venture-200/g' \
        -e 's/bg-blue-500/bg-venture-500/g' \
        -e 's/bg-blue-600/bg-venture-600/g' \
        -e 's/bg-blue-700/bg-venture-700/g' \
        -e 's/text-blue-50/text-venture-50/g' \
        -e 's/text-blue-100/text-venture-100/g' \
        -e 's/text-blue-500/text-venture-500/g' \
        -e 's/text-blue-600/text-venture-600/g' \
        -e 's/text-blue-700/text-venture-700/g' \
        -e 's/text-blue-800/text-venture-800/g' \
        -e 's/text-blue-900/text-venture-900/g' \
        -e 's/border-blue-200/border-venture-200/g' \
        -e 's/border-blue-300/border-venture-300/g' \
        -e 's/border-blue-500/border-venture-500/g' \
        -e 's/hover:bg-blue-50/hover:bg-venture-50/g' \
        -e 's/hover:bg-blue-100/hover:bg-venture-100/g' \
        -e 's/hover:bg-blue-600/hover:bg-venture-600/g' \
        -e 's/hover:text-blue-600/hover:text-venture-600/g' \
        -e 's/hover:text-blue-700/hover:text-venture-700/g' \
        -e 's/focus:ring-blue-500/focus:ring-venture-500/g' \
        -e 's/ring-blue-500/ring-venture-500/g' \
        "$file"
    
    # Replace generic primary colors with venture colors
    sed -i \
        -e 's/bg-primary\b/bg-venture-500/g' \
        -e 's/text-primary\b/text-venture-600/g' \
        -e 's/border-primary\b/border-venture-500/g' \
        -e 's/hover:bg-primary/hover:bg-venture-600/g' \
        -e 's/focus:ring-primary/focus:ring-venture-500/g' \
        "$file"
    
    # Clean up backup files
    rm -f "$file.bak"
}

# Update all TypeScript React files in components
echo "🔄 Updating component files..."
find components/ -name "*.tsx" -type f | while read -r file; do
    update_colors "$file"
done

# Update specific high-priority files
echo "🎯 Updating high-priority components..."

# Update dashboard layout if it exists
if [ -f "components/layouts/dashboardLayout.tsx" ]; then
    update_colors "components/layouts/dashboardLayout.tsx"
fi

# Update any remaining layout files
find components/layouts/ -name "*.tsx" -type f 2>/dev/null | while read -r file; do
    update_colors "$file"
done

# Update form components
find components/forms/ -name "*.tsx" -type f 2>/dev/null | while read -r file; do
    update_colors "$file"
done

# Update navigation components
find components/navigation/ -name "*.tsx" -type f 2>/dev/null | while read -r file; do
    update_colors "$file"
done

echo "✅ Color standardization complete!"
echo ""
echo "📊 Summary:"
echo "  - Backup created in: $BACKUP_DIR"
echo "  - Updated all .tsx files in components/"
echo "  - Replaced blue-* classes with venture-*"
echo "  - Replaced generic primary classes with venture-*"
echo ""
echo "🔍 Next steps:"
echo "1. Review changes: git diff"
echo "2. Test the application"
echo "3. Commit changes if satisfied"
echo "4. Remove backup if everything works: rm -rf $BACKUP_DIR"
echo ""
echo "🎨 VentureDirection now uses consistent blue & white branding!"
