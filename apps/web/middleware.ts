import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'
import createIntlMiddleware from 'next-intl/middleware'
import { routing } from './i18n/routing'

// Create the internationalization middleware
const intlMiddleware = createIntlMiddleware(routing)

// Define public routes that don't require authentication
const isPublicRoute = createRouteMatcher([
  '/',
  '/(en|fr|es|de|ie|ng|ee)',
  '/(en|fr|es|de|ie|ng|ee)/sign-in(.*)',
  '/(en|fr|es|de|ie|ng|ee)/sign-up(.*)',
  '/sign-in(.*)',
  '/sign-up(.*)',
  '/dashboard(.*)',
  '/(en|fr|es|de|ie|ng|ee)/dashboard(.*)',
  '/api/health',
  '/api/webhooks(.*)',
])

// Define API routes that need authentication
const isApiRoute = createRouteMatcher(['/api(.*)'])

// Define business routes that need authentication and business context
const isBusinessRoute = createRouteMatcher([
  '/business(.*)',
  '/(en|fr|es|de|ie|ng|ee)/business(.*)',
])

// Define business group routes that need authentication and business group context
const isBusinessGroupRoute = createRouteMatcher([
  '/business-group(.*)',
  '/(en|fr|es|de|ie|ng|ee)/business-group(.*)',
])

// Define other protected routes that need authentication but not business context
const isProtectedRoute = createRouteMatcher([
])

// Business feature routes are now part of dashboard routes (session-scoped)

export default clerkMiddleware(async (auth, req) => {
  const { userId, sessionClaims } = await auth()
  const { pathname } = req.nextUrl

  // Handle API routes first (no i18n needed)
  if (isApiRoute(req)) {
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Inject business context headers for API routes
    const response = NextResponse.next()
    if (sessionClaims?.metadata) {
      const metadata = sessionClaims.metadata as any
      if (metadata.activeBusinessId) {
        response.headers.set('x-business-id', metadata.activeBusinessId)
      }
      if (metadata.activeBusinessGroupId) {
        response.headers.set('x-business-group-id', metadata.activeBusinessGroupId)
      }
    }
    return response
  }

  // Apply internationalization middleware for non-API routes
  const intlResponse = intlMiddleware(req)

  // Inject business context headers for authenticated routes
  if (userId && sessionClaims?.metadata) {
    const metadata = sessionClaims.metadata as any
    if (metadata.activeBusinessId) {
      intlResponse.headers.set('x-business-id', metadata.activeBusinessId)
    }
    if (metadata.activeBusinessGroupId) {
      intlResponse.headers.set('x-business-group-id', metadata.activeBusinessGroupId)
    }
  }

  // Allow public routes
  if (isPublicRoute(req)) {
    console.log('Public route:', pathname)
    return intlResponse
  }

  // Handle protected routes that need authentication
  const needsAuth = isBusinessRoute(req) || isBusinessGroupRoute(req) || isProtectedRoute(req)

  if (needsAuth) {
    if (!userId) {
      const signInUrl = new URL('/en/sign-in', req.url)
      signInUrl.searchParams.set('redirect_url', pathname)
      return NextResponse.redirect(signInUrl)
    }

    // Business routes need active business context
    if (isBusinessRoute(req)) {
      const metadata = sessionClaims?.metadata as any
      if (!metadata?.activeBusinessId) {
        // Extract locale from pathname for redirect
        const localeMatch = pathname.match(/^\/([a-z]{2})\//)
        const locale = localeMatch ? localeMatch[1] : 'en'
        const selectBusinessUrl = `/${locale}/select-business`
        return NextResponse.redirect(new URL(selectBusinessUrl, req.url))
      }
    }

    // Business group routes need active business group context
    if (isBusinessGroupRoute(req)) {
      const metadata = sessionClaims?.metadata as any
      if (!metadata?.activeBusinessGroupId) {
        // Extract locale from pathname for redirect
        const localeMatch = pathname.match(/^\/([a-z]{2})\//)
        const locale = localeMatch ? localeMatch[1] : 'en'
        const dashboardUrl = `/${locale}/dashboard`
        return NextResponse.redirect(new URL(dashboardUrl, req.url))
      }
    }

    return intlResponse
  }

  // Business feature routes are now handled as dashboard routes with session context

  // Redirect authenticated users from root to dashboard
  // if ((pathname === '/' || pathname.match(/^\/(en|fr|es|de|ie|ng|ee)\/?$/)) && userId) {
  //   return NextResponse.redirect(new URL('/dashboard', req.url))
  // }

  // Redirect unauthenticated users to sign-in
  if (!userId) {
    const signInUrl = new URL('/en/sign-in', req.url)
    signInUrl.searchParams.set('redirect_url', pathname)
    return NextResponse.redirect(signInUrl)
  }

  return intlResponse
})

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
}