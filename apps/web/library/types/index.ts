// Central export for all types
// This makes it easy to import types from a single location

// Business types
export type * from './business'

// Auth types

export type * from './auth'

// Compliance types
export type * from './compliance'

// User types
export type * from './user'

// Document types
export type * from './document'

// Common types
export type * from './common'

// Re-export Prisma types for convenience
export type {
  // Enums
  BusinessRegistrationStatus,
  BusinessSystemStatus,
  BusinessStatus,
  ScopeType,
  ResourceType,
  ComplianceStatus,
  
  // Models (base types without relations)
  Business,
  BusinessGroup,
  User,
  Compliance,
  ComplianceTemplate,
  ComplianceRule,
  Document,
  Note,
  SecureVault,
  Integration,
  Country,
  Industries,
  Role,
  Permissions,
  RoleAssignment,
  Scope,
  Subscription,
  EmailAddresses,
  userSetting as UserSetting,
  Addresses,
  AddressRelationship,
  BusinessPresence,
  BusinessIndustry,
  BusinessWithGroupRelationship,
  RolePermission,
  RoleHierarchy,
  PermissionResourceRelationship,
  EmailPreferences,
  IntegrationInstallation,
} from '@prisma/client'
