# useOnboarding Hook

## 📖 Overview

The `useOnboarding` hook manages business onboarding progress tracking. It provides functionality to track completion of essential business setup steps and guide users through the initial configuration process.

## 🎯 Purpose

- **Progress Tracking**: Monitor completion of 7 core business modules
- **Auto-Detection**: Automatically detect completed features based on actual data
- **User Guidance**: Provide contextual onboarding checklists
- **Business Scoped**: Each business has its own onboarding progress

## 📝 Signature

```typescript
function useOnboarding(): UseOnboardingReturn
```

### Return Value

```typescript
interface UseOnboardingReturn {
  onboardingStatus: BusinessOnboardingStatus | null
  isLoading: boolean
  error: string | null
  refreshStatus: () => Promise<void>
  updateProgress: (module: OnboardingFeatureModule, completed: boolean) => Promise<void>
  initializeOnboarding: () => Promise<void>
}
```

### Core Types

```typescript
interface BusinessOnboardingStatus {
  businessId: string
  totalSteps: number
  completedSteps: number
  completionPercentage: number
  isFullyOnboarded: boolean
  steps: OnboardingStep[]
  lastUpdated: Date
}

interface OnboardingStep {
  module: OnboardingFeatureModule
  title: string
  description: string
  completed: boolean
  completedAt?: Date
  actionUrl: string
  icon: string
}

type OnboardingFeatureModule = 
  | 'store'      // Add products/services
  | 'orders'     // Create first order/invoice
  | 'finance'    // Set up payments/transactions
  | 'crm'        // Add customers
  | 'compliance' // Complete compliance tasks
  | 'documents'  // Upload business documents
  | 'settings'   // Customize business profile
```

## 🚀 Basic Usage

### Onboarding Progress Display

```typescript
import { useOnboarding } from '@/lib/hooks/useOnboarding'

function OnboardingProgress() {
  const { onboardingStatus, isLoading, error } = useOnboarding()
  
  if (isLoading) return <div>Loading onboarding status...</div>
  if (error) return <div>Error: {error}</div>
  if (!onboardingStatus) return null
  
  return (
    <div className="onboarding-progress">
      <h2>Business Setup Progress</h2>
      <div className="progress-bar">
        <div 
          className="progress-fill"
          style={{ width: `${onboardingStatus.completionPercentage}%` }}
        />
      </div>
      <p>
        {onboardingStatus.completedSteps} of {onboardingStatus.totalSteps} completed
        ({onboardingStatus.completionPercentage}%)
      </p>
    </div>
  )
}
```

### Onboarding Checklist

```typescript
function OnboardingChecklist() {
  const { onboardingStatus, updateProgress, isLoading } = useOnboarding()
  
  if (!onboardingStatus) return null
  
  const handleStepComplete = async (module: OnboardingFeatureModule) => {
    try {
      await updateProgress(module, true)
    } catch (error) {
      console.error('Failed to update progress:', error)
    }
  }
  
  return (
    <div className="onboarding-checklist">
      <h3>Setup Checklist</h3>
      {onboardingStatus.steps.map((step) => (
        <div key={step.module} className="checklist-item">
          <div className="flex items-center gap-3">
            <input
              type="checkbox"
              checked={step.completed}
              onChange={() => !step.completed && handleStepComplete(step.module)}
              disabled={step.completed || isLoading}
            />
            <div className="flex-1">
              <h4>{step.title}</h4>
              <p className="text-sm text-gray-600">{step.description}</p>
              {step.completed && step.completedAt && (
                <p className="text-xs text-green-600">
                  Completed {new Date(step.completedAt).toLocaleDateString()}
                </p>
              )}
            </div>
            {!step.completed && (
              <a href={step.actionUrl} className="btn-primary">
                Set Up
              </a>
            )}
          </div>
        </div>
      ))}
    </div>
  )
}
```

### Compact Progress Widget

```typescript
function CompactOnboardingWidget() {
  const { onboardingStatus } = useOnboarding()
  
  if (!onboardingStatus || onboardingStatus.isFullyOnboarded) {
    return null
  }
  
  return (
    <div className="compact-onboarding">
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
          <span className="text-white text-sm font-medium">
            {onboardingStatus.completionPercentage}%
          </span>
        </div>
        <div>
          <h4 className="font-medium">Complete Your Setup</h4>
          <p className="text-sm text-gray-600">
            {onboardingStatus.totalSteps - onboardingStatus.completedSteps} steps remaining
          </p>
        </div>
        <a href="/onboard" className="btn-outline">
          Continue
        </a>
      </div>
    </div>
  )
}
```

## 🔄 Advanced Usage

### Auto-Detection Integration

```typescript
function BusinessDashboard() {
  const { onboardingStatus, refreshStatus } = useOnboarding()
  
  // Refresh onboarding status when user completes actions
  const handleOrderCreated = async () => {
    // Create order logic...
    await createOrder(orderData)
    
    // Refresh onboarding to detect completion
    await refreshStatus()
  }
  
  const handleProductAdded = async () => {
    // Add product logic...
    await addProduct(productData)
    
    // Refresh onboarding status
    await refreshStatus()
  }
  
  return (
    <div>
      <h1>Business Dashboard</h1>
      
      {/* Show onboarding if not complete */}
      {onboardingStatus && !onboardingStatus.isFullyOnboarded && (
        <OnboardingBanner />
      )}
      
      <div className="dashboard-actions">
        <button onClick={handleOrderCreated}>
          Create Order
        </button>
        <button onClick={handleProductAdded}>
          Add Product
        </button>
      </div>
    </div>
  )
}
```

### Conditional Feature Access

```typescript
function FeatureGate({ children, requiredModule }) {
  const { onboardingStatus } = useOnboarding()
  
  if (!onboardingStatus) return <div>Loading...</div>
  
  const moduleStep = onboardingStatus.steps.find(
    step => step.module === requiredModule
  )
  
  if (!moduleStep?.completed) {
    return (
      <div className="feature-gate">
        <h3>Complete Setup Required</h3>
        <p>Please complete {moduleStep?.title} to access this feature.</p>
        <a href={moduleStep?.actionUrl} className="btn-primary">
          Complete Setup
        </a>
      </div>
    )
  }
  
  return <>{children}</>
}

// Usage
function AdvancedAnalytics() {
  return (
    <FeatureGate requiredModule="finance">
      <AnalyticsDashboard />
    </FeatureGate>
  )
}
```

### Progress-Based Recommendations

```typescript
function OnboardingRecommendations() {
  const { onboardingStatus } = useOnboarding()
  
  if (!onboardingStatus) return null
  
  // Get next recommended step
  const nextStep = onboardingStatus.steps.find(step => !step.completed)
  
  // Get completion-based recommendations
  const getRecommendations = () => {
    const { completionPercentage } = onboardingStatus
    
    if (completionPercentage < 30) {
      return "Start with basic business information and add your first product."
    } else if (completionPercentage < 60) {
      return "Great progress! Now set up your financial and customer management."
    } else if (completionPercentage < 90) {
      return "Almost there! Complete compliance and document management."
    } else {
      return "Excellent! Your business is fully set up and ready to go."
    }
  }
  
  return (
    <div className="onboarding-recommendations">
      <h3>Next Steps</h3>
      <p>{getRecommendations()}</p>
      
      {nextStep && (
        <div className="next-step">
          <h4>Recommended: {nextStep.title}</h4>
          <p>{nextStep.description}</p>
          <a href={nextStep.actionUrl} className="btn-primary">
            Get Started
          </a>
        </div>
      )}
    </div>
  )
}
```

## 🎯 Integration Patterns

### Dashboard Integration

```typescript
function BusinessDashboard() {
  const { onboardingStatus } = useOnboarding()
  const showOnboarding = onboardingStatus && !onboardingStatus.isFullyOnboarded
  
  return (
    <div className="dashboard">
      {/* Onboarding banner for incomplete setup */}
      {showOnboarding && (
        <div className="onboarding-banner">
          <OnboardingProgress />
          <CompactOnboardingWidget />
        </div>
      )}
      
      {/* Main dashboard content */}
      <DashboardContent />
      
      {/* Completion celebration */}
      {onboardingStatus?.isFullyOnboarded && (
        <CompletionCelebration />
      )}
    </div>
  )
}
```

### Navigation Integration

```typescript
function BusinessNavigation() {
  const { onboardingStatus } = useOnboarding()
  
  const getNavItemBadge = (module: OnboardingFeatureModule) => {
    if (!onboardingStatus) return null
    
    const step = onboardingStatus.steps.find(s => s.module === module)
    if (!step?.completed) {
      return <Badge variant="warning">Setup Required</Badge>
    }
    return null
  }
  
  return (
    <nav>
      <NavItem href="/store">
        Store
        {getNavItemBadge('store')}
      </NavItem>
      <NavItem href="/orders">
        Orders
        {getNavItemBadge('orders')}
      </NavItem>
      <NavItem href="/finance">
        Finance
        {getNavItemBadge('finance')}
      </NavItem>
      {/* ... more nav items */}
    </nav>
  )
}
```

### Page-Level Integration

```typescript
function StorePage() {
  const { onboardingStatus, updateProgress } = useOnboarding()
  
  const storeStep = onboardingStatus?.steps.find(s => s.module === 'store')
  const isStoreSetupComplete = storeStep?.completed
  
  const handleFirstProductAdded = async () => {
    // Product creation logic...
    
    // Mark store setup as complete
    if (!isStoreSetupComplete) {
      await updateProgress('store', true)
    }
  }
  
  return (
    <div>
      <h1>Store Management</h1>
      
      {!isStoreSetupComplete && (
        <OnboardingPrompt
          title="Set up your store"
          description="Add your first product to complete store setup"
          onComplete={handleFirstProductAdded}
        />
      )}
      
      <StoreContent />
    </div>
  )
}
```

## 🚨 Common Pitfalls

### 1. **Business Context Dependency**

```typescript
// ❌ Hook depends on current business context
const { onboardingStatus } = useOnboarding() // Uses current business

// ✅ Ensure business is selected
const { currentBusiness } = useBusiness()
if (!currentBusiness) return <SelectBusiness />

const { onboardingStatus } = useOnboarding() // Now safe to use
```

### 2. **Manual vs Auto-Detection**

```typescript
// ❌ Only manual updates
await updateProgress('store', true) // Manual only

// ✅ Combine with auto-detection
await updateProgress('store', true)
await refreshStatus() // Triggers auto-detection for all modules
```

### 3. **Progress State Management**

```typescript
// ❌ Not handling loading states
const { updateProgress } = useOnboarding()
const handleComplete = () => updateProgress('store', true) // No loading state

// ✅ Proper loading handling
const { updateProgress, isLoading } = useOnboarding()
const [updating, setUpdating] = useState(false)

const handleComplete = async () => {
  setUpdating(true)
  try {
    await updateProgress('store', true)
  } finally {
    setUpdating(false)
  }
}
```

## 🎯 Best Practices

1. **Use within business context (requires current business)**
2. **Combine manual updates with auto-detection**
3. **Handle loading and error states properly**
4. **Provide clear next steps to users**
5. **Celebrate completion milestones**
6. **Test onboarding flows thoroughly**
7. **Keep onboarding steps actionable and clear**
