import { jest } from '@jest/globals'

// Mock data generators for web app
export const mockVentureUser = (overrides: any = {}) => ({
  id: 'user-123',
  clerkUserId: 'clerk-456',
  firstName: '<PERSON>',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '+1234567890',
  language: 'en',
  timezone: 'UTC',
  activeBusinessId: 'business-789',
  activeBusinessGroupId: 'group-101',
  accessibleBusinessIds: ['business-789'],
  accessibleBusinessGroupIds: ['group-101'],
  emailAddresses: ['<EMAIL>'],
  userSettings: {
    theme: 'light',
    notifications: true,
    language: 'en'
  },
  roles: [
    {
      id: 'role-1',
      name: 'Business Owner',
      scope: 'BUSINESS',
      businessId: 'business-789'
    }
  ],
  permissions: [
    {
      id: 'perm-1',
      name: 'manage_business',
      resource: 'business',
      action: 'manage'
    }
  ],
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  ...overrides
})

export const mockBusiness = (overrides: any = {}) => ({
  id: 'business-123',
  name: 'Test Business',
  registrationNumber: 'REG123456',
  countryId: 'US',
  status: 'ACTIVE',
  systemStatus: 'ACTIVE',
  registrationStatus: 'REGISTERED',
  description: 'A test business for development',
  website: 'https://testbusiness.com',
  industry: 'Technology',
  employeeCount: 10,
  foundedYear: 2020,
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  country: {
    id: 'US',
    name: 'United States',
    code: 'US',
    currency: 'USD'
  },
  businessIndustries: [
    {
      industry: {
        id: 'tech',
        name: 'Technology',
        description: 'Technology sector'
      }
    }
  ],
  _count: {
    compliance: 5,
    notes: 3,
    secureVaults: 2
  },
  ...overrides
})

export const mockCompliance = (overrides: any = {}) => ({
  id: 'compliance-123',
  title: 'Annual Tax Filing',
  description: 'Submit annual tax returns to IRS',
  businessId: 'business-123',
  status: 'PENDING',
  priority: 'HIGH',
  dueDate: new Date('2024-12-31'),
  completedAt: null,
  notes: 'Remember to gather all receipts',
  category: 'TAX',
  estimatedHours: 8,
  assignedTo: 'user-123',
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  business: {
    id: 'business-123',
    name: 'Test Business'
  },
  ...overrides
})

export const mockUserBusinessMembership = (overrides: any = {}) => ({
  id: 'membership-123',
  userId: 'user-123',
  businessId: 'business-123',
  businessGroupId: null,
  role: 'OWNER',
  isActive: true,
  joinedAt: new Date('2024-01-01'),
  lastAccessedAt: new Date('2024-01-15'),
  permissions: ['manage_business', 'view_compliance'],
  ...overrides
})

export const mockBusinessGroup = (overrides: any = {}) => ({
  id: 'group-123',
  name: 'Main Business Group',
  description: 'Primary business group for organization',
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  businessWithGroupRelationships: [
    {
      businessId: 'business-123',
      business: {
        id: 'business-123',
        name: 'Test Business'
      }
    }
  ],
  ...overrides
})

// API Response helpers
export const mockApiResponse = <T>(data: T, overrides: any = {}) => ({
  success: true,
  data,
  message: 'Operation completed successfully',
  timestamp: new Date().toISOString(),
  requestId: 'req-123',
  ...overrides
})

export const mockApiError = (message: string = 'API Error', status: number = 500, code?: string) => ({
  response: {
    status,
    statusText: status === 404 ? 'Not Found' : status === 401 ? 'Unauthorized' : 'Internal Server Error',
    data: {
      success: false,
      message,
      error: code || 'GENERIC_ERROR',
      timestamp: new Date().toISOString(),
      requestId: 'req-error-123'
    }
  }
})

// Prisma mock helpers
export const createMockPrismaClient = () => ({
  user: {
    findUnique: jest.fn(),
    findMany: jest.fn(),
    findFirst: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    upsert: jest.fn(),
    count: jest.fn()
  },
  business: {
    findUnique: jest.fn(),
    findMany: jest.fn(),
    findFirst: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    upsert: jest.fn(),
    count: jest.fn()
  },
  compliance: {
    findUnique: jest.fn(),
    findMany: jest.fn(),
    findFirst: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    upsert: jest.fn(),
    count: jest.fn()
  },
  userBusinessMembership: {
    findUnique: jest.fn(),
    findMany: jest.fn(),
    findFirst: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    upsert: jest.fn(),
    count: jest.fn()
  },
  businessGroup: {
    findUnique: jest.fn(),
    findMany: jest.fn(),
    findFirst: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    upsert: jest.fn(),
    count: jest.fn()
  },
  country: {
    findUnique: jest.fn(),
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    upsert: jest.fn()
  },
  $transaction: jest.fn(),
  $connect: jest.fn(),
  $disconnect: jest.fn(),
  $executeRaw: jest.fn(),
  $executeRawUnsafe: jest.fn(),
  $queryRaw: jest.fn(),
  $queryRawUnsafe: jest.fn()
})

// Test assertion helpers
export const expectApiCall = (mockFn: jest.MockedFunction<any>, endpoint: string, data?: any) => {
  if (data) {
    expect(mockFn).toHaveBeenCalledWith(endpoint, data)
  } else {
    expect(mockFn).toHaveBeenCalledWith(endpoint)
  }
}

export const expectPrismaQuery = (mockFn: jest.MockedFunction<any>, expectedQuery: any) => {
  expect(mockFn).toHaveBeenCalledWith(expectedQuery)
}

export const expectBusinessAccess = (mockFn: jest.MockedFunction<any>, userId: string, businessId: string) => {
  expect(mockFn).toHaveBeenCalledWith({
    where: {
      userId,
      businessId,
      isActive: true
    }
  })
}

// Error simulation helpers
export const simulateNetworkError = () => {
  const error = new Error('Network connection failed')
  error.name = 'NetworkError'
  return error
}

export const simulateTimeoutError = () => {
  const error = new Error('Request timeout')
  error.name = 'TimeoutError'
  return error
}

export const simulateValidationError = (fields: string[]) => ({
  response: {
    status: 400,
    data: {
      success: false,
      message: 'Validation failed',
      errors: fields.map(field => ({
        field,
        message: `${field} is required`
      }))
    }
  }
})

export const simulateAuthError = (message: string = 'Unauthorized') => ({
  response: {
    status: 401,
    data: {
      success: false,
      message,
      error: 'UNAUTHORIZED'
    }
  }
})

export const simulateForbiddenError = (message: string = 'Forbidden') => ({
  response: {
    status: 403,
    data: {
      success: false,
      message,
      error: 'FORBIDDEN'
    }
  }
})

export const simulateNotFoundError = (resource: string = 'Resource') => ({
  response: {
    status: 404,
    data: {
      success: false,
      message: `${resource} not found`,
      error: 'NOT_FOUND'
    }
  }
})

// Date and time helpers
export const mockCurrentDate = (dateString: string) => {
  const mockDate = new Date(dateString)
  jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any)
  return mockDate
}

export const restoreDate = () => {
  jest.restoreAllMocks()
}

// Async test helpers
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

export const flushPromises = () => new Promise(resolve => setImmediate(resolve))

// Mock environment helpers
export const mockEnvVar = (key: string, value: string) => {
  const originalValue = process.env[key]
  process.env[key] = value
  return () => {
    if (originalValue === undefined) {
      delete process.env[key]
    } else {
      process.env[key] = originalValue
    }
  }
}
