# useVentureUser Hook

## 📖 Overview

The `useVentureUser` hook is the primary interface for accessing VentureDirection's internal user model. It bridges authentication providers (Clerk, WorkOS) with the platform's user management system, providing role-based access control and business context.

## 🎯 Purpose

- **Provider Agnostic**: Works with Clerk, WorkOS, or any auth provider
- **Business Context Aware**: Includes roles and permissions per business
- **Performance Optimized**: Client-side caching with configurable timeout
- **Type Safe**: Full TypeScript support with comprehensive interfaces

## 📝 Signature

```typescript
function useVentureUser(options?: UseVentureUserOptions): VentureUserContextData
```

### Parameters

```typescript
interface UseVentureUserOptions extends ResolveUserOptions {
  autoFetch?: boolean        // Auto-fetch on mount (default: true)
  cacheTimeout?: number      // Cache timeout in ms (default: 5 minutes)
  includeRoles?: boolean     // Include role data (default: true)
  includePermissions?: boolean // Include permission data (default: true)
  includeBusinessContext?: boolean // Include business memberships (default: true)
  businessId?: string        // Specific business context
  businessGroupId?: string   // Specific business group context
}
```

### Return Value

```typescript
interface VentureUserContextData {
  // Core user data
  user: VentureUser | null
  isLoading: boolean
  error: string | null
  
  // Actions
  refetch: () => Promise<void>
  
  // Permission helpers
  hasPermission: (permission: string, businessId?: string) => boolean
  hasRole: (role: string, businessId?: string) => boolean
  canAccess: (resource: string, action: string, businessId?: string) => boolean
  
  // Business context helpers
  getCurrentBusinessRole: () => string | null
  getCurrentBusinessPermissions: () => string[]
  isOwnerOfBusiness: (businessId: string) => boolean
  isAdminOfBusiness: (businessId: string) => boolean
}
```

## 🚀 Basic Usage

### Simple User Data

```typescript
import { useVentureUser } from '@/lib/hooks/useVentureUser'

function UserProfile() {
  const { user, isLoading, error } = useVentureUser()
  
  if (isLoading) return <div>Loading...</div>
  if (error) return <div>Error: {error}</div>
  if (!user) return <div>Please sign in</div>
  
  return (
    <div>
      <h1>Welcome, {user.displayName}!</h1>
      <p>Email: {user.email}</p>
      <p>Joined: {new Date(user.createdAt).toLocaleDateString()}</p>
    </div>
  )
}
```

### Permission Checks

```typescript
function AdminPanel() {
  const { user, hasPermission, hasRole } = useVentureUser()
  
  if (!user) return <LoginPrompt />
  
  return (
    <div>
      <h1>Admin Panel</h1>
      
      {hasPermission('users.manage') && (
        <UserManagementSection />
      )}
      
      {hasRole('admin') && (
        <AdminSettings />
      )}
      
      {hasPermission('finance.view') && (
        <FinancialReports />
      )}
    </div>
  )
}
```

### Business Context

```typescript
function BusinessDashboard() {
  const { 
    user, 
    getCurrentBusinessRole, 
    getCurrentBusinessPermissions,
    isOwnerOfBusiness 
  } = useVentureUser()
  
  const currentRole = getCurrentBusinessRole()
  const permissions = getCurrentBusinessPermissions()
  
  return (
    <div>
      <h1>Business Dashboard</h1>
      <p>Your role: {currentRole}</p>
      <p>Permissions: {permissions.length}</p>
      
      {user?.businessMemberships.map(membership => (
        <div key={membership.businessId}>
          <h3>{membership.businessName}</h3>
          <p>Role: {membership.userRole}</p>
          {isOwnerOfBusiness(membership.businessId) && (
            <Badge>Owner</Badge>
          )}
        </div>
      ))}
    </div>
  )
}
```

## ⚡ Performance Optimization

### Caching Configuration

```typescript
// Default 5-minute cache
const { user } = useVentureUser()

// Custom cache timeout (10 minutes)
const { user } = useVentureUser({
  cacheTimeout: 10 * 60 * 1000
})

// Disable auto-fetch
const { user, refetch } = useVentureUser({
  autoFetch: false
})

// Manual fetch when needed
useEffect(() => {
  if (someCondition) {
    refetch()
  }
}, [someCondition, refetch])
```

### Selective Data Loading

```typescript
// Basic user data only (fastest)
const { user } = useVentureUser({
  includeRoles: false,
  includePermissions: false,
  includeBusinessContext: false
})

// Full context (includes everything)
const { user } = useVentureUser({
  includeRoles: true,
  includePermissions: true,
  includeBusinessContext: true
})

// Specific business context
const { user } = useVentureUser({
  businessId: 'specific-business-id'
})
```

## 🔄 Advanced Usage

### Error Handling

```typescript
function RobustUserComponent() {
  const { user, isLoading, error, refetch } = useVentureUser()
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <Spinner />
        <span>Loading user data...</span>
      </div>
    )
  }
  
  if (error) {
    return (
      <div className="text-red-600 p-4">
        <p>Failed to load user data: {error}</p>
        <button onClick={refetch} className="mt-2 btn-primary">
          Try Again
        </button>
      </div>
    )
  }
  
  if (!user) {
    return <LoginPrompt />
  }
  
  return <UserInterface user={user} />
}
```

### Cross-Business Permission Checks

```typescript
function MultiBusinessManager() {
  const { user, hasPermission, isOwnerOfBusiness } = useVentureUser()
  
  if (!user) return <LoginPrompt />
  
  return (
    <div>
      <h1>Manage Multiple Businesses</h1>
      
      {user.businessMemberships.map(membership => (
        <div key={membership.businessId} className="business-card">
          <h3>{membership.businessName}</h3>
          <p>Role: {membership.userRole}</p>
          
          {/* Check permissions for specific business */}
          {hasPermission('users.manage', membership.businessId) && (
            <button>Manage Users</button>
          )}
          
          {isOwnerOfBusiness(membership.businessId) && (
            <button>Transfer Ownership</button>
          )}
        </div>
      ))}
    </div>
  )
}
```

### Conditional Rendering Patterns

```typescript
function ConditionalFeatures() {
  const { user, hasPermission, hasRole } = useVentureUser()
  
  // Early return for unauthenticated users
  if (!user) return <PublicView />
  
  return (
    <div>
      {/* Always visible to authenticated users */}
      <UserDashboard />
      
      {/* Permission-based features */}
      {hasPermission('finance.view') && (
        <FinancialWidget />
      )}
      
      {hasPermission('users.manage') && (
        <UserManagementWidget />
      )}
      
      {/* Role-based features */}
      {hasRole('admin') && (
        <AdminWidget />
      )}
      
      {user.isBusinessOwner && (
        <OwnerWidget />
      )}
      
      {/* Complex permission logic */}
      {(hasPermission('reports.view') || hasRole('manager')) && (
        <ReportsWidget />
      )}
    </div>
  )
}
```

## 🎣 Related Hooks

### useVentureUserBasic

Simplified version without business context for better performance:

```typescript
import { useVentureUserBasic } from '@/lib/hooks/useVentureUser'

function SimpleUserInfo() {
  const { user, isLoading } = useVentureUserBasic()
  
  if (isLoading) return <Spinner />
  
  return (
    <div>
      <img src={user?.avatar} alt={user?.displayName} />
      <span>{user?.displayName}</span>
    </div>
  )
}
```

### usePermission

Check a single permission:

```typescript
import { usePermission } from '@/lib/hooks/useVentureUser'

function ProtectedButton() {
  const { hasPermission, isLoading } = usePermission('users.manage')
  
  if (isLoading) return <ButtonSkeleton />
  if (!hasPermission) return null
  
  return <button>Manage Users</button>
}
```

### useRole

Check a single role:

```typescript
import { useRole } from '@/lib/hooks/useVentureUser'

function AdminBadge() {
  const { hasRole } = useRole('admin')
  
  return hasRole ? <Badge>Admin</Badge> : null
}
```

## 🚨 Common Pitfalls

### 1. **Business Context Confusion**

```typescript
// ❌ May not work as expected - which business?
const { hasPermission } = useVentureUser()
const canManage = hasPermission('users.manage')

// ✅ Explicit business context
const { hasPermission } = useVentureUser({ businessId: 'specific-id' })
const canManage = hasPermission('users.manage')

// ✅ Or use business-scoped hook
const { hasPermission } = useBusinessUser() // Auto-scoped to current business
```

### 2. **Cache Invalidation**

```typescript
// ❌ Stale data after user changes
const { user } = useVentureUser()
// User updates profile elsewhere...

// ✅ Manual refresh when needed
const { user, refetch } = useVentureUser()
// After profile update:
await refetch()
```

### 3. **Loading State Handling**

```typescript
// ❌ Flickering UI
const { user } = useVentureUser()
return user ? <UserContent /> : <LoginPrompt />

// ✅ Proper loading states
const { user, isLoading } = useVentureUser()
if (isLoading) return <LoadingSpinner />
return user ? <UserContent /> : <LoginPrompt />
```

## 🔧 TypeScript Support

### User Interface

```typescript
interface VentureUser {
  id: string
  email: string
  displayName: string
  avatar?: string
  isActive: boolean
  currentBusinessId?: string
  businessMemberships: VentureUserBusinessContext[]
  currentRoles: VentureUserRole[]
  currentPermissions: VentureUserPermission[]
  isBusinessOwner: boolean
  isBusinessAdmin: boolean
  // ... more properties
}
```

### Permission Helpers

```typescript
// Type-safe permission checks
const hasPermission: (permission: string, businessId?: string) => boolean
const hasRole: (role: string, businessId?: string) => boolean
const canAccess: (resource: string, action: string, businessId?: string) => boolean
```

## 🎯 Best Practices

1. **Use appropriate options for your use case**
2. **Handle loading and error states consistently**
3. **Leverage caching for performance**
4. **Use business-scoped hooks when in business context**
5. **Implement proper error boundaries**
6. **Test permission edge cases thoroughly**
