# VentureDirection Colour Usage Guide

## 🎨 Quick Reference

### Primary Colours
```css
/* Main brand blue - use for primary actions */
bg-venture-500    /* #3b82f6 - Primary buttons, logos */
text-venture-600  /* #2563eb - Primary text, links */
border-venture-200 /* #bfdbfe - Borders, dividers */

/* Hover and active states */
hover:bg-venture-50   /* Light blue hover */
hover:bg-venture-600  /* Darker blue hover for buttons */
active:bg-venture-700 /* Active state for buttons */
```

### Semantic Colours
```css
/* Success (Green) */
bg-success-500    /* #22c55e - Success buttons */
text-success-600  /* #16a34a - Success text */
bg-success-100    /* #dcfce7 - Success backgrounds */

/* Warning (Amber) */
bg-warning-500    /* #f59e0b - Warning buttons */
text-warning-600  /* #d97706 - Warning text */
bg-warning-100    /* #fef3c7 - Warning backgrounds */

/* Error (Red) */
bg-error-500      /* #ef4444 - Error buttons */
text-error-600    /* #dc2626 - Error text */
bg-error-100      /* #fee2e2 - Error backgrounds */
```

## 🎯 Component Usage

### Buttons

#### Primary Actions
```tsx
<Button variant="default">
  Save Changes
</Button>
// Result: bg-venture-500 text-white hover:bg-venture-600
```

#### Secondary Actions
```tsx
<Button variant="outline">
  Cancel
</Button>
// Result: border-venture-200 text-venture-600 hover:bg-venture-50
```

#### Success Actions
```tsx
<Button variant="success">
  Approve
</Button>
// Result: bg-success-500 text-white hover:bg-success-600
```

#### Destructive Actions
```tsx
<Button variant="destructive">
  Delete
</Button>
// Result: bg-error-500 text-white hover:bg-error-600
```

### Badges

#### Status Badges
```tsx
<Badge variant="default">Active</Badge>     // Blue theme
<Badge variant="success">Completed</Badge>  // Green
<Badge variant="warning">Pending</Badge>    // Amber
<Badge variant="error">Failed</Badge>       // Red
<Badge variant="outline">Draft</Badge>      // Blue outline
```

### Navigation

#### Active Navigation Items
```tsx
className="bg-venture-500 text-white shadow-sm"
```

#### Hover States
```tsx
className="hover:bg-venture-50 hover:text-venture-700"
```

#### Default Navigation Text
```tsx
className="text-gray-600"
```

### Cards

#### Default Cards
```tsx
<div className="bg-white border border-gray-200 rounded-lg shadow-sm">
  Standard card content
</div>
```

#### Primary Accent Cards
```tsx
<div className="bg-venture-50 border border-venture-200 rounded-lg">
  Emphasized content
</div>
```

#### High Emphasis Cards
```tsx
<div className="bg-venture-500 text-white rounded-lg shadow-md">
  Important content
</div>
```

## 🏗️ Layout Guidelines

### Page Backgrounds
```css
bg-white          /* Main content areas */
bg-gray-50        /* Page backgrounds */
bg-venture-50     /* Emphasized sections */
```

### Text Hierarchy
```css
text-gray-900     /* Primary headings */
text-gray-700     /* Secondary headings */
text-gray-600     /* Body text */
text-gray-500     /* Secondary text */
text-venture-600  /* Primary links and emphasis */
```

### Borders and Dividers
```css
border-gray-200   /* Standard borders */
border-venture-200 /* Primary borders */
border-gray-300   /* Input borders */
```

## 🎨 Business Feature Colours

### Feature Icons
All business feature icons use `text-venture-600`:

```tsx
<Store className="h-5 w-5 text-venture-600" />
<ShoppingCart className="h-5 w-5 text-venture-600" />
<Users className="h-5 w-5 text-venture-600" />
<DollarSign className="h-5 w-5 text-venture-600" />
```

### Business Logos
```tsx
<div className="h-8 w-8 rounded bg-venture-500 flex items-center justify-center">
  <Building2 className="h-4 w-4 text-white" />
</div>
```

## 📊 Status Indicators

### Business Status
```css
/* Active business */
bg-success-500    /* Green dot */
text-success-600  /* Green text */

/* Pending/Processing */
bg-warning-500    /* Amber dot */
text-warning-600  /* Amber text */

/* Error/Inactive */
bg-error-500      /* Red dot */
text-error-600    /* Red text */

/* Neutral/Draft */
bg-gray-400       /* Gray dot */
text-gray-600     /* Gray text */
```

### Order Status
```tsx
// Order status mapping
const statusColours = {
  pending: 'bg-warning-100 text-warning-800',
  confirmed: 'bg-venture-100 text-venture-800',
  processing: 'bg-venture-100 text-venture-800',
  shipped: 'bg-venture-100 text-venture-800',
  delivered: 'bg-success-100 text-success-800',
  cancelled: 'bg-error-100 text-error-800',
}
```

## 🔧 Form Elements

### Input Fields
```css
/* Default state */
border-gray-300 focus:border-venture-500 focus:ring-venture-500

/* Error state */
border-error-300 focus:border-error-500 focus:ring-error-500

/* Success state */
border-success-300 focus:border-success-500 focus:ring-success-500
```

### Labels
```css
text-gray-700     /* Standard labels */
text-error-600    /* Error labels */
text-success-600  /* Success labels */
```

### Help Text
```css
text-gray-500     /* Standard help text */
text-error-500    /* Error messages */
text-success-500  /* Success messages */
```

## 🎯 Interactive States

### Hover States
```css
/* Buttons */
hover:bg-venture-600    /* Primary button hover */
hover:bg-venture-50     /* Secondary hover */

/* Links */
hover:text-venture-700  /* Link hover */

/* Cards */
hover:bg-venture-50     /* Card hover */
hover:shadow-md         /* Elevation change */
```

### Focus States
```css
focus:ring-2 focus:ring-venture-500 focus:ring-offset-2
```

### Active States
```css
active:bg-venture-700   /* Button active */
active:bg-venture-100   /* Secondary active */
```

## ♿ Accessibility

### Contrast Ratios
All colour combinations meet WCAG AA standards:

- **venture-600 on white**: 7.2:1 ✅
- **gray-900 on white**: 18.7:1 ✅
- **gray-600 on white**: 5.7:1 ✅
- **white on venture-500**: 5.9:1 ✅

### Colour Blind Friendly
- Use icons alongside colours for status
- Provide text labels for colour-coded information
- Ensure sufficient contrast for all users

## 🚫 What NOT to Do

### Avoid These Patterns
```css
/* Don't use random blue shades */
❌ bg-blue-400, bg-blue-700, bg-sky-500

/* Don't use pure black text */
❌ text-black

/* Don't mix different primary colours */
❌ bg-blue-500 with text-indigo-600

/* Don't forget hover states */
❌ <button className="bg-venture-500">No hover</button>

/* Don't use colours without semantic meaning */
❌ <span className="text-red-500">Random red text</span>
```

### Use These Instead
```css
/* Use venture-* colours consistently */
✅ bg-venture-500, text-venture-600

/* Use gray-900 for dark text */
✅ text-gray-900

/* Use semantic colour variants */
✅ text-error-600 for error messages
✅ text-success-600 for success messages

/* Always include hover states */
✅ hover:bg-venture-600

/* Use semantic colours appropriately */
✅ text-error-600 for error messages
```

## 🎨 Custom Colour Utilities

### Venture Gradient
```css
.venture-gradient {
  background: linear-gradient(135deg, rgb(59 130 246) 0%, rgb(37 99 235) 100%);
}
```

### Venture Shadow
```css
.venture-shadow {
  box-shadow: 0 4px 6px -1px rgb(59 130 246 / 0.1), 0 2px 4px -2px rgb(59 130 246 / 0.1);
}
```

### Usage
```tsx
<div className="venture-gradient text-white p-6 rounded-lg">
  Gradient background
</div>

<div className="bg-white venture-shadow rounded-lg p-6">
  Blue-tinted shadow
</div>
```

## 🚀 Implementation Checklist

### For Each Component:
- [ ] Uses venture-* colours for primary elements
- [ ] Has proper hover states
- [ ] Includes focus states for accessibility
- [ ] Uses semantic colours appropriately
- [ ] Meets contrast ratio requirements
- [ ] Follows consistent patterns

### For Each Page:
- [ ] White or gray-50 background
- [ ] Consistent text hierarchy
- [ ] Proper status indicators
- [ ] Accessible colour combinations
- [ ] VentureDirection branding elements

This guide ensures consistent, accessible, and professional use of VentureDirection's blue and white colour scheme across all components and pages.
