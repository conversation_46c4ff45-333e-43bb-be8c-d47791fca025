{"name": "native", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"dev": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "clean": "rm -rf node_modules && rm -rf .expo", "typecheck": "tsc --noEmit"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-navigation/native": "^7.1.10", "app": "*", "expo": "~53.0.6", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-linking": "~7.1.4", "expo-router": "~5.0.3", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.6", "expo-web-browser": "~14.1.6", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "^2.25.0", "react-native-reanimated": "3.16.2", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "4.11.1", "react-native-web": "~0.20.0", "tailwindcss": "^3.4.17", "ui": "*"}, "devDependencies": {"@babel/core": "^7.20.5", "@types/react": "^19", "typescript": "^5.8.3"}, "private": true}