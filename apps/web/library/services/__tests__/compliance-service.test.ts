import { ComplianceService } from '../compliance-service'
import { prisma } from '@/apps/web/db'

// Mock Prisma completely
jest.mock('@/apps/web/db', () => ({
  prisma: {
    compliance: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      upsert: jest.fn(),
      count: jest.fn(),
    },
    complianceTemplate: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
    },
    business: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
    },
    userBusinessMembership: {
      findFirst: jest.fn(),
      findMany: jest.fn(),
    },
    $transaction: jest.fn(),
    $connect: jest.fn(),
    $disconnect: jest.fn(),
  }
}))

const mockedPrisma = prisma as jest.Mocked<typeof prisma>

describe('ComplianceService', () => {
  let complianceService: ComplianceService

  beforeEach(() => {
    jest.clearAllMocks()
    complianceService = new ComplianceService()
  })

  describe('getCompliance', () => {
    it('should return compliance with business access control', async () => {
      const userId = 'user-123'
      const filters = { businessId: 'business-123' }
      
      // Mock user business access
      mockedPrisma.userBusinessMembership.findFirst.mockResolvedValue({
        userId,
        businessId: 'business-123',
        isActive: true,
        role: 'OWNER'
      } as any)

      const mockCompliance = [
        {
          id: 'comp-1',
          title: 'Tax Filing',
          description: 'Annual tax filing requirement',
          status: 'PENDING',
          priority: 'HIGH',
          dueDate: new Date('2024-12-31'),
          businessId: 'business-123',
          business: {
            id: 'business-123',
            name: 'Test Business'
          }
        },
        {
          id: 'comp-2',
          title: 'License Renewal',
          description: 'Business license renewal',
          status: 'IN_PROGRESS',
          priority: 'MEDIUM',
          dueDate: new Date('2024-06-30'),
          businessId: 'business-123',
          business: {
            id: 'business-123',
            name: 'Test Business'
          }
        }
      ]

      mockedPrisma.compliance.findMany.mockResolvedValue(mockCompliance as any)

      const result = await complianceService.getCompliance(filters, userId)

      expect(mockedPrisma.userBusinessMembership.findFirst).toHaveBeenCalledWith({
        where: {
          userId,
          businessId: 'business-123',
          isActive: true
        }
      })

      expect(mockedPrisma.compliance.findMany).toHaveBeenCalledWith({
        where: {
          businessId: 'business-123'
        },
        include: {
          business: {
            select: {
              id: true,
              name: true
            }
          }
        },
        orderBy: {
          dueDate: 'asc'
        }
      })

      expect(result).toEqual(mockCompliance)
      expect(result).toHaveLength(2)
    })

    it('should throw error when user has no access to business', async () => {
      const userId = 'user-no-access'
      const filters = { businessId: 'business-123' }

      mockedPrisma.userBusinessMembership.findFirst.mockResolvedValue(null)

      await expect(complianceService.getCompliance(filters, userId))
        .rejects.toThrow('Access denied: User does not have access to this business')

      expect(mockedPrisma.compliance.findMany).not.toHaveBeenCalled()
    })

    it('should return all accessible compliance when no business filter', async () => {
      const userId = 'user-123'
      const accessibleBusinessIds = ['business-1', 'business-2']

      // Mock user accessible businesses
      mockedPrisma.userBusinessMembership.findMany.mockResolvedValue([
        { businessId: 'business-1', isActive: true },
        { businessId: 'business-2', isActive: true }
      ] as any)

      const mockCompliance = [
        {
          id: 'comp-1',
          title: 'Tax Filing',
          businessId: 'business-1'
        },
        {
          id: 'comp-2',
          title: 'License Renewal',
          businessId: 'business-2'
        }
      ]

      mockedPrisma.compliance.findMany.mockResolvedValue(mockCompliance as any)

      const result = await complianceService.getCompliance({}, userId)

      expect(mockedPrisma.compliance.findMany).toHaveBeenCalledWith({
        where: {
          businessId: {
            in: accessibleBusinessIds
          }
        },
        include: {
          business: {
            select: {
              id: true,
              name: true
            }
          }
        },
        orderBy: {
          dueDate: 'asc'
        }
      })

      expect(result).toEqual(mockCompliance)
    })
  })

  describe('getComplianceById', () => {
    it('should return compliance by ID with access control', async () => {
      const complianceId = 'comp-123'
      const userId = 'user-123'

      const mockCompliance = {
        id: complianceId,
        title: 'Tax Filing',
        description: 'Annual tax filing',
        status: 'PENDING',
        businessId: 'business-123',
        business: {
          id: 'business-123',
          name: 'Test Business'
        }
      }

      mockedPrisma.compliance.findUnique.mockResolvedValue(mockCompliance as any)
      mockedPrisma.userBusinessMembership.findFirst.mockResolvedValue({
        userId,
        businessId: 'business-123',
        isActive: true
      } as any)

      const result = await complianceService.getComplianceById(complianceId, userId)

      expect(mockedPrisma.compliance.findUnique).toHaveBeenCalledWith({
        where: { id: complianceId },
        include: {
          business: {
            select: {
              id: true,
              name: true
            }
          }
        }
      })

      expect(result).toEqual(mockCompliance)
    })

    it('should throw error when compliance not found', async () => {
      const complianceId = 'non-existent'
      const userId = 'user-123'

      mockedPrisma.compliance.findUnique.mockResolvedValue(null)

      await expect(complianceService.getComplianceById(complianceId, userId))
        .rejects.toThrow('Compliance requirement not found')
    })
  })

  describe('createCompliance', () => {
    it('should create compliance with business access validation', async () => {
      const userId = 'user-123'
      const complianceData = {
        title: 'New Compliance',
        description: 'A new compliance requirement',
        businessId: 'business-123',
        dueDate: new Date('2024-12-31'),
        priority: 'MEDIUM'
      }

      // Mock business access
      mockedPrisma.userBusinessMembership.findFirst.mockResolvedValue({
        userId,
        businessId: 'business-123',
        isActive: true,
        role: 'OWNER'
      } as any)

      const mockCreatedCompliance = {
        id: 'new-comp-id',
        ...complianceData,
        status: 'PENDING',
        createdAt: new Date(),
        updatedAt: new Date()
      }

      mockedPrisma.compliance.create.mockResolvedValue(mockCreatedCompliance as any)

      const result = await complianceService.createCompliance(userId, complianceData)

      expect(mockedPrisma.userBusinessMembership.findFirst).toHaveBeenCalledWith({
        where: {
          userId,
          businessId: 'business-123',
          isActive: true
        }
      })

      expect(mockedPrisma.compliance.create).toHaveBeenCalledWith({
        data: {
          ...complianceData,
          status: 'PENDING'
        },
        include: {
          business: {
            select: {
              id: true,
              name: true
            }
          }
        }
      })

      expect(result).toEqual(mockCreatedCompliance)
    })

    it('should throw error when user cannot create compliance for business', async () => {
      const userId = 'user-no-access'
      const complianceData = {
        title: 'New Compliance',
        businessId: 'business-123',
        dueDate: new Date('2024-12-31')
      }

      mockedPrisma.userBusinessMembership.findFirst.mockResolvedValue(null)

      await expect(complianceService.createCompliance(userId, complianceData as any))
        .rejects.toThrow('Access denied: User cannot create compliance for this business')

      expect(mockedPrisma.compliance.create).not.toHaveBeenCalled()
    })
  })

  describe('getOverdueCompliance', () => {
    it('should return overdue compliance for user businesses', async () => {
      const userId = 'user-123'
      const accessibleBusinessIds = ['business-1', 'business-2']

      mockedPrisma.userBusinessMembership.findMany.mockResolvedValue([
        { businessId: 'business-1' },
        { businessId: 'business-2' }
      ] as any)

      const mockOverdueCompliance = [
        {
          id: 'comp-1',
          title: 'Overdue Tax Filing',
          dueDate: new Date('2024-01-01'), // Past date
          status: 'PENDING',
          businessId: 'business-1'
        }
      ]

      mockedPrisma.compliance.findMany.mockResolvedValue(mockOverdueCompliance as any)

      const result = await complianceService.getOverdueCompliance(userId)

      expect(mockedPrisma.compliance.findMany).toHaveBeenCalledWith({
        where: {
          businessId: {
            in: accessibleBusinessIds
          },
          dueDate: {
            lt: expect.any(Date)
          },
          status: {
            in: ['PENDING', 'IN_PROGRESS']
          }
        },
        include: {
          business: {
            select: {
              id: true,
              name: true
            }
          }
        },
        orderBy: {
          dueDate: 'asc'
        }
      })

      expect(result).toEqual(mockOverdueCompliance)
    })
  })

  describe('getUpcomingCompliance', () => {
    it('should return upcoming compliance within specified days', async () => {
      const userId = 'user-123'
      const days = 30
      const accessibleBusinessIds = ['business-1']

      mockedPrisma.userBusinessMembership.findMany.mockResolvedValue([
        { businessId: 'business-1' }
      ] as any)

      const mockUpcomingCompliance = [
        {
          id: 'comp-1',
          title: 'Upcoming License Renewal',
          dueDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 days from now
          status: 'PENDING',
          businessId: 'business-1'
        }
      ]

      mockedPrisma.compliance.findMany.mockResolvedValue(mockUpcomingCompliance as any)

      const result = await complianceService.getUpcomingCompliance(userId, days)

      expect(mockedPrisma.compliance.findMany).toHaveBeenCalledWith({
        where: {
          businessId: {
            in: accessibleBusinessIds
          },
          dueDate: {
            gte: expect.any(Date),
            lte: expect.any(Date)
          },
          status: {
            in: ['PENDING', 'IN_PROGRESS']
          }
        },
        include: {
          business: {
            select: {
              id: true,
              name: true
            }
          }
        },
        orderBy: {
          dueDate: 'asc'
        }
      })

      expect(result).toEqual(mockUpcomingCompliance)
    })
  })
})
