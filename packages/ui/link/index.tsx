import React from 'react'
import type { UniversalLinkProps } from './index.types'

// Conditional import for expo-router
let ExpoLink: any = null
try {
  ExpoLink = require('expo-router').Link
} catch {
  // Fallback for web environments where expo-router is not available
  ExpoLink = ({ children, href, ...props }: any) => (
    <a href={href} {...props}>{children}</a>
  )
}

/* --- <Link/> --------------------------------------------------------------------------------- */

export const Link = (props: UniversalLinkProps) => {
    // Props
    const {
        children,
        href,
        style,
        replace,
        onPress,
        target,
        asChild,
        push,
        testID,
        nativeID,
        allowFontScaling,
        numberOfLines,
        maxFontSizeMultiplier
    } = props

    // -- Render --

    return (
        <ExpoLink
            href={href as any}
            style={style}
            onPress={onPress}
            target={target}
            asChild={asChild}
            replace={replace}
            push={push}
            testID={testID}
            nativeID={nativeID}
            allowFontScaling={allowFontScaling}
            numberOfLines={numberOfLines}
            maxFontSizeMultiplier={maxFontSizeMultiplier}
        >
            {children}
        </ExpoLink>
    )
}

