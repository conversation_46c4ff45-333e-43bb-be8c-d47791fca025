#!/usr/bin/env node

const { spawn } = require('child_process')
const path = require('path')

// Test runner configuration
const TEST_CONFIGS = {
  unit: {
    description: 'Run unit tests (packages/lib)',
    command: 'yarn',
    args: ['test:unit'],
    env: { NODE_ENV: 'test' }
  },
  integration: {
    description: 'Run integration tests (API routes)',
    command: 'yarn',
    args: ['test:integration'],
    env: { NODE_ENV: 'test' }
  },
  api: {
    description: 'Run API tests',
    command: 'yarn',
    args: ['test:api'],
    env: { NODE_ENV: 'test' }
  },
  services: {
    description: 'Run service tests',
    command: 'yarn',
    args: ['test:services'],
    env: { NODE_ENV: 'test' }
  },
  component: {
    description: 'Run component tests (UI)',
    command: 'yarn',
    args: ['test:component'],
    env: { NODE_ENV: 'test' }
  },
  all: {
    description: 'Run all tests',
    command: 'yarn',
    args: ['test'],
    env: { NODE_ENV: 'test' }
  },
  coverage: {
    description: 'Run tests with coverage',
    command: 'yarn',
    args: ['test:coverage'],
    env: { NODE_ENV: 'test' }
  },
  ci: {
    description: 'Run tests in CI mode',
    command: 'yarn',
    args: ['test:ci'],
    env: { NODE_ENV: 'test', CI: 'true' }
  }
}

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`
}

function printUsage() {
  console.log(colorize('\n🧪 VentureDirection Test Runner\n', 'cyan'))
  console.log(colorize('Usage:', 'bright'))
  console.log('  node scripts/test-runner.js <test-type> [options]\n')
  
  console.log(colorize('Available test types:', 'bright'))
  Object.entries(TEST_CONFIGS).forEach(([key, config]) => {
    console.log(`  ${colorize(key.padEnd(12), 'green')} - ${config.description}`)
  })
  
  console.log(colorize('\nOptions:', 'bright'))
  console.log('  --watch     Run tests in watch mode')
  console.log('  --verbose   Run tests with verbose output')
  console.log('  --silent    Run tests with minimal output')
  console.log('  --help      Show this help message')
  
  console.log(colorize('\nExamples:', 'bright'))
  console.log('  node scripts/test-runner.js api --watch')
  console.log('  node scripts/test-runner.js services --verbose')
  console.log('  node scripts/test-runner.js all --coverage')
  console.log('')
}

function runTest(testType, options = {}) {
  const config = TEST_CONFIGS[testType]
  
  if (!config) {
    console.error(colorize(`❌ Unknown test type: ${testType}`, 'red'))
    printUsage()
    process.exit(1)
  }

  console.log(colorize(`\n🚀 Running ${testType} tests...`, 'cyan'))
  console.log(colorize(`📝 ${config.description}\n`, 'yellow'))

  // Prepare command arguments
  let args = [...config.args]
  
  if (options.watch) {
    args.push('--watch')
  }
  
  if (options.verbose) {
    args.push('--verbose')
  }
  
  if (options.silent) {
    args.push('--silent')
  }

  // Prepare environment
  const env = {
    ...process.env,
    ...config.env
  }

  // Spawn the test process
  const testProcess = spawn(config.command, args, {
    stdio: 'inherit',
    env,
    cwd: process.cwd()
  })

  testProcess.on('close', (code) => {
    if (code === 0) {
      console.log(colorize(`\n✅ ${testType} tests completed successfully!`, 'green'))
    } else {
      console.log(colorize(`\n❌ ${testType} tests failed with exit code ${code}`, 'red'))
      process.exit(code)
    }
  })

  testProcess.on('error', (error) => {
    console.error(colorize(`\n💥 Failed to start test process: ${error.message}`, 'red'))
    process.exit(1)
  })

  // Handle process termination
  process.on('SIGINT', () => {
    console.log(colorize('\n\n🛑 Test execution interrupted', 'yellow'))
    testProcess.kill('SIGINT')
    process.exit(0)
  })
}

function parseArguments() {
  const args = process.argv.slice(2)
  
  if (args.length === 0 || args.includes('--help')) {
    printUsage()
    process.exit(0)
  }

  const testType = args[0]
  const options = {
    watch: args.includes('--watch'),
    verbose: args.includes('--verbose'),
    silent: args.includes('--silent')
  }

  return { testType, options }
}

// Pre-flight checks
function preflightChecks() {
  console.log(colorize('🔍 Running pre-flight checks...', 'blue'))
  
  // Check if we're in the right directory
  const packageJsonPath = path.join(process.cwd(), 'package.json')
  try {
    const packageJson = require(packageJsonPath)
    if (packageJson.name !== 'venturedirection') {
      console.warn(colorize('⚠️  Warning: Not in VentureDirection root directory', 'yellow'))
    }
  } catch (error) {
    console.error(colorize('❌ package.json not found. Are you in the project root?', 'red'))
    process.exit(1)
  }

  // Check if node_modules exists
  const nodeModulesPath = path.join(process.cwd(), 'node_modules')
  try {
    require('fs').accessSync(nodeModulesPath)
  } catch (error) {
    console.error(colorize('❌ node_modules not found. Run "yarn install" first.', 'red'))
    process.exit(1)
  }

  console.log(colorize('✅ Pre-flight checks passed\n', 'green'))
}

// Main execution
function main() {
  try {
    preflightChecks()
    const { testType, options } = parseArguments()
    runTest(testType, options)
  } catch (error) {
    console.error(colorize(`💥 Unexpected error: ${error.message}`, 'red'))
    process.exit(1)
  }
}

// Run if called directly
if (require.main === module) {
  main()
}

module.exports = {
  runTest,
  TEST_CONFIGS,
  printUsage
}
