import { Stack } from "expo-router";
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from "react-native-gesture-handler";
import "../global.css";
import { QueryProvider } from '@/providers/query-provider';

export default function RootLayout() {
  return (
    <GestureHandlerRootView className="flex-1">
      <SafeAreaProvider>
        <QueryProvider>
          <Stack>
            <Stack.Screen name="index" />
            {/* <Stack.Screen name="auth" options={{ headerShown: false }} /> */}
          </Stack>
        </QueryProvider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}
