# Testing Setup Documentation

## Overview

This project uses a **dual Jest configuration** to handle different types of testing across the monorepo structure.

## 📁 Configuration Structure

### 1. **Root Level** (`jest.config.ts` + `jest.setup.ts`)
- **Purpose**: Tests shared packages and utilities
- **Scope**: `packages/lib`, `packages/ui`
- **Environment**: Node.js
- **Focus**: Unit tests for shared libraries

### 2. **Web App Level** (`apps/web/jest.config.js` + `apps/web/jest.setup.ts`)
- **Purpose**: Tests web application logic - Mainly focus on backend
- **Scope**: API clients, services, business logic
- **Environment**: Node.js with proper mocking
- **Focus**: Service and API testing

## 🎯 Why Two Configurations?

### **Separation of Concerns**
- **Root**: Simple, focused on packages
- **Web App**: Complex mocking for database, APIs, authentication

### **Avoiding Conflicts**
- Different module resolution strategies
- Different mocking requirements
- Independent test environments

### **Performance**
- Faster test execution by avoiding unnecessary setup
- Isolated test environments

## 🚀 Available Commands

### **Web App Testing (Primary Focus)**
```bash
# API client tests
yarn test:api

# Service layer tests  
yarn test:services

# Watch mode
yarn test:api:watch
yarn test:services:watch

# Enhanced test runner
yarn test:runner api
yarn test:runner services
```

### **Package Testing**
```bash
# Root level package tests
yarn test:lib

# All tests (both root and web app)
yarn test
```

## 📊 Current Test Coverage

### **Web App Tests** ✅
- **API Tests**: 41 tests passing (3 suites)
  - BusinessApi, ComplianceApi, AuthApi
- **Service Tests**: 26 tests passing (3 suites)  
  - BusinessService, ComplianceService, BusinessContextService
- **Total**: 67 tests covering critical business logic

### **Package Tests** 📦
- Ready for shared utility testing
- Focused on reusable components and libraries

## 🔧 Configuration Details

### **Root Jest Config** (`jest.config.ts`)
```typescript
{
  preset: 'ts-jest',
  testEnvironment: 'node',
  testMatch: ['<rootDir>/packages/**/*.(test|spec).(ts|tsx)'],
  // Ignores web app tests to avoid conflicts
}
```

### **Web App Jest Config** (`apps/web/jest.config.js`)
```javascript
{
  preset: 'ts-jest', 
  testEnvironment: 'node',
  testMatch: [
    '<rootDir>/__tests__/**/*.(test|spec).(ts|tsx)',
    '<rootDir>/library/**/__tests__/**/*.(test|spec).(ts|tsx)'
  ],
  // Comprehensive mocking for Prisma, APIs, etc.
}
```

## 🎨 Test Organization

```
├── jest.config.ts              # Root config (packages)
├── jest.setup.ts               # Root setup (minimal)
├── apps/web/
│   ├── jest.config.js          # Web app config
│   ├── jest.setup.ts           # Web app setup (comprehensive)
│   ├── __tests__/
│   │   ├── api/                # API client tests
│   │   └── utils/              # Test utilities
│   └── library/services/
│       └── __tests__/          # Service layer tests
└── packages/
    └── lib/
        └── __tests__/          # Package tests (future)
```

## 🔍 Key Features

### **Comprehensive Mocking**
- **Prisma**: Complete database mocking
- **API Clients**: HTTP request/response mocking
- **Authentication**: User session mocking
- **Environment**: Test-specific variables

### **Test-Driven Development**
- Tests written first to define expected behavior
- Services implemented to make tests pass
- Ensures robust, well-designed APIs

### **Real-World Scenarios**
- Authentication & authorization edge cases
- Business access control validation
- Error handling for network, validation, permissions
- Data relationships and complex filtering

## 🎯 Best Practices

### **When to Use Each Config**

**Use Root Config For:**
- Shared utility functions
- UI component libraries
- Pure business logic functions
- Cross-app functionality

**Use Web App Config For:**
- API client testing
- Service layer testing
- Database interaction testing
- Authentication flow testing

### **Running Tests**

**Development:**
```bash
# Focus on specific areas
yarn test:api:watch
yarn test:services:watch
```

**CI/CD:**
```bash
# Run all tests
yarn test:api && yarn test:services
```

**Enhanced Experience:**
```bash
# Colored output with pre-flight checks
yarn test:runner api --verbose
```

## 🚀 Future Enhancements

- **Integration Tests**: End-to-end API testing
- **Component Tests**: UI component testing with React Testing Library
- **Performance Tests**: Load testing for critical services
- **Visual Regression**: Screenshot testing for UI components

---

## **🔧 TypeScript & ESLint Configuration**

### **Test File Exclusions**

Both TypeScript and ESLint are configured to **ignore all test files and directories** to prevent type checking and linting issues during development.

#### **Root Configuration** (`tsconfig.json` & `eslint.config.mjs`)
```json
"exclude": [
  "**/__tests__/**",
  "**/__test__/**",
  "**/*.test.ts",
  "**/*.test.tsx",
  "**/*.spec.ts",
  "**/*.spec.tsx",
  "**/jest.setup.ts",
  "**/jest.config.ts"
]
```

#### **Web App Configuration** (`apps/web/tsconfig.json` & `apps/web/.eslintrc.js`)
```json
"exclude": [
  "**/__tests__/**",
  "**/__test__/**",
  "**/*.test.ts",
  "**/*.test.tsx",
  "**/*.spec.ts",
  "**/*.spec.tsx",
  "**/jest.setup.ts",
  "**/jest.config.ts"
]
```

### **Benefits**
- ✅ **No TypeScript errors** from test files during development
- ✅ **No ESLint warnings** from test-specific code patterns
- ✅ **Faster compilation** by excluding test files
- ✅ **Clean IDE experience** without test file noise
- ✅ **Separate test environment** with proper Jest configuration
- ✅ **Independent web app config** - No Expo dependencies for testing

### **Web App TypeScript Fix**
The web app now uses an **independent TypeScript configuration** that doesn't extend from the root Expo config, ensuring tests run without Expo dependencies while maintaining proper path mappings for the application.

---

This dual configuration approach ensures **clean separation**, **fast execution**, and **comprehensive coverage** for both shared packages and web application logic.
