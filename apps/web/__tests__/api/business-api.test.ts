import { BusinessApi } from '@/packages/lib/api/business-api'
import { apiClient } from '@/packages/lib/api/client'
import { API_ENDPOINTS } from '@/packages/lib/api/endpoints'

// Mock the API client
jest.mock('@/packages/lib/api/client', () => ({
  apiClient: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    patch: jest.fn(),
    delete: jest.fn(),
    updateBaseURL: jest.fn(),
    setAuthToken: jest.fn()
  }
}))

const mockedApiClient = apiClient as jest.Mocked<typeof apiClient>

describe('BusinessApi', () => {
  let businessApi: BusinessApi

  beforeEach(() => {
    jest.clearAllMocks()
    businessApi = new BusinessApi()
  })

  describe('getBusinesses', () => {
    it('should fetch all businesses successfully', async () => {
      const mockBusinesses = [
        { 
          id: 'business-1', 
          name: 'Test Business 1', 
          status: 'ACTIVE',
          countryId: 'US',
          registrationNumber: 'REG001'
        },
        { 
          id: 'business-2', 
          name: 'Test Business 2', 
          status: 'ACTIVE',
          countryId: 'CA',
          registrationNumber: 'REG002'
        }
      ]
      
      mockedApiClient.get.mockResolvedValue(mockBusinesses)

      const result = await businessApi.getBusinesses()

      expect(mockedApiClient.get).toHaveBeenCalledWith(API_ENDPOINTS.BUSINESSES)
      expect(result).toEqual(mockBusinesses)
      expect(result).toHaveLength(2)
    })

    it('should handle empty business list', async () => {
      mockedApiClient.get.mockResolvedValue([])

      const result = await businessApi.getBusinesses()

      expect(result).toEqual([])
      expect(result).toHaveLength(0)
    })

    it('should handle API errors gracefully', async () => {
      const apiError = new Error('Network error')
      mockedApiClient.get.mockRejectedValue(apiError)

      await expect(businessApi.getBusinesses()).rejects.toThrow('Network error')
      expect(mockedApiClient.get).toHaveBeenCalledWith(API_ENDPOINTS.BUSINESSES)
    })
  })

  describe('getBusinessById', () => {
    it('should fetch business by ID successfully', async () => {
      const businessId = 'business-123'
      const mockBusiness = {
        id: businessId,
        name: 'Test Business',
        status: 'ACTIVE',
        countryId: 'US',
        registrationNumber: 'REG123',
        description: 'A test business',
        website: 'https://test.com'
      }

      mockedApiClient.get.mockResolvedValue(mockBusiness)

      const result = await businessApi.getBusinessById(businessId)

      expect(mockedApiClient.get).toHaveBeenCalledWith(API_ENDPOINTS.BUSINESS_BY_ID(businessId))
      expect(result).toEqual(mockBusiness)
      expect(result.id).toBe(businessId)
    })

    it('should handle business not found', async () => {
      const businessId = 'non-existent'
      const notFoundError = {
        response: {
          status: 404,
          data: { message: 'Business not found' }
        }
      }
      
      mockedApiClient.get.mockRejectedValue(notFoundError)

      await expect(businessApi.getBusinessById(businessId)).rejects.toEqual(notFoundError)
    })
  })

  describe('createBusiness', () => {
    it('should create business successfully', async () => {
      const createData = {
        name: 'New Business',
        countryId: 'US',
        registrationNumber: '123456789',
        description: 'A new business'
      }
      
      const mockCreatedBusiness = {
        id: 'new-business-id',
        ...createData,
        status: 'PENDING',
        createdAt: new Date().toISOString()
      }

      mockedApiClient.post.mockResolvedValue(mockCreatedBusiness)

      const result = await businessApi.createBusiness(createData)

      expect(mockedApiClient.post).toHaveBeenCalledWith(API_ENDPOINTS.BUSINESSES, createData)
      expect(result).toEqual(mockCreatedBusiness)
      expect(result.name).toBe(createData.name)
    })

    it('should handle validation errors', async () => {
      const invalidData = { name: '' } // Missing required fields
      const validationError = {
        response: {
          status: 400,
          data: {
            message: 'Validation failed',
            errors: ['Name is required', 'Country is required']
          }
        }
      }

      mockedApiClient.post.mockRejectedValue(validationError)

      await expect(businessApi.createBusiness(invalidData as any)).rejects.toEqual(validationError)
    })
  })

  describe('updateBusiness', () => {
    it('should update business successfully', async () => {
      const businessId = 'business-123'
      const updateData = { 
        name: 'Updated Business Name',
        description: 'Updated description'
      }
      
      const mockUpdatedBusiness = {
        id: businessId,
        ...updateData,
        status: 'ACTIVE',
        updatedAt: new Date().toISOString()
      }

      mockedApiClient.put.mockResolvedValue(mockUpdatedBusiness)

      const result = await businessApi.updateBusiness(businessId, updateData)

      expect(mockedApiClient.put).toHaveBeenCalledWith(
        API_ENDPOINTS.BUSINESS_BY_ID(businessId),
        updateData
      )
      expect(result).toEqual(mockUpdatedBusiness)
    })
  })

  describe('deleteBusiness', () => {
    it('should delete business successfully', async () => {
      const businessId = 'business-123'
      mockedApiClient.delete.mockResolvedValue(undefined)

      await businessApi.deleteBusiness(businessId)

      expect(mockedApiClient.delete).toHaveBeenCalledWith(API_ENDPOINTS.BUSINESS_BY_ID(businessId))
    })

    it('should handle delete permission errors', async () => {
      const businessId = 'business-123'
      const forbiddenError = {
        response: {
          status: 403,
          data: { message: 'Insufficient permissions to delete business' }
        }
      }

      mockedApiClient.delete.mockRejectedValue(forbiddenError)

      await expect(businessApi.deleteBusiness(businessId)).rejects.toEqual(forbiddenError)
    })
  })

  describe('getBusinessCompliance', () => {
    it('should fetch business compliance data', async () => {
      const businessId = 'business-123'
      const mockCompliance = [
        {
          id: 'comp-1',
          title: 'Tax Filing',
          status: 'PENDING',
          businessId,
          dueDate: '2024-12-31'
        },
        {
          id: 'comp-2',
          title: 'License Renewal',
          status: 'COMPLETED',
          businessId,
          dueDate: '2024-06-30'
        }
      ]

      mockedApiClient.get.mockResolvedValue(mockCompliance)

      const result = await businessApi.getBusinessCompliance(businessId)

      expect(mockedApiClient.get).toHaveBeenCalledWith(
        `${API_ENDPOINTS.COMPLIANCE}?businessId=${businessId}`
      )
      expect(result).toEqual(mockCompliance)
      expect(result).toHaveLength(2)
    })
  })

  describe('getBusinessDocuments', () => {
    it('should fetch business documents', async () => {
      const businessId = 'business-123'
      const mockDocuments = [
        {
          id: 'doc-1',
          name: 'Business Certificate.pdf',
          businessId,
          uploadedAt: '2024-01-15'
        },
        {
          id: 'doc-2',
          name: 'Tax Document.pdf',
          businessId,
          uploadedAt: '2024-02-01'
        }
      ]

      mockedApiClient.get.mockResolvedValue(mockDocuments)

      const result = await businessApi.getBusinessDocuments(businessId)

      expect(mockedApiClient.get).toHaveBeenCalledWith(
        `${API_ENDPOINTS.DOCUMENTS}?businessId=${businessId}`
      )
      expect(result).toEqual(mockDocuments)
      expect(result).toHaveLength(2)
    })
  })
})
