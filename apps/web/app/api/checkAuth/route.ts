import { NextRequest, NextResponse } from 'next/server'
import { getVentureUser } from '@/apps/web/library/services/venture-user-service'

export async function GET(_request: NextRequest) {
  try {
    const ventureUser = await getVentureUser({ includeRoles: true, includePermissions: true });
    return NextResponse.json(ventureUser)
  } catch (_error: any) {
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
