import { apiClient } from './client'
import { API_ENDPOINTS } from './endpoints'
import type { Business, CreateBusinessData, UpdateBusinessData } from '@/apps/web/library/types/business'

export class BusinessApi {
  async getBusinesses(): Promise<Business[]> {
    return apiClient.get<Business[]>(API_ENDPOINTS.BUSINESSES)
  }

  async getBusinessById(id: string): Promise<Business> {
    return apiClient.get<Business>(API_ENDPOINTS.BUSINESS_BY_ID(id))
  }

  async createBusiness(data: CreateBusinessData): Promise<Business> {
    return apiClient.post<Business>(API_ENDPOINTS.BUSINESSES, data)
  }

  async updateBusiness(id: string, data: UpdateBusinessData): Promise<Business> {
    return apiClient.put<Business>(API_ENDPOINTS.BUSINESS_BY_ID(id), data)
  }

  async deleteBusiness(id: string): Promise<void> {
    return apiClient.delete<void>(API_ENDPOINTS.BUSINESS_BY_ID(id))
  }

  async getBusinessCompliance(businessId: string) {
    return apiClient.get(`${API_ENDPOINTS.COMPLIANCE}?businessId=${businessId}`)
  }

  async getBusinessDocuments(businessId: string) {
    return apiClient.get(`${API_ENDPOINTS.DOCUMENTS}?businessId=${businessId}`)
  }
}

// Export singleton instance
export const businessApi = new BusinessApi()
