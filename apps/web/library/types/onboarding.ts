export type OnboardingFeatureModule = 
  | 'store'
  | 'orders' 
  | 'finance'
  | 'crm'
  | 'compliance'
  | 'documents'
  | 'settings'

export interface OnboardingStep {
  module: OnboardingFeatureModule
  title: string
  description: string
  completed: boolean
  completedAt?: Date
  actionUrl: string
  icon: string
}

export interface BusinessOnboardingStatus {
  businessId: string
  totalSteps: number
  completedSteps: number
  completionPercentage: number
  isFullyOnboarded: boolean
  steps: OnboardingStep[]
  lastUpdated: Date
}

export interface OnboardingProgressUpdate {
  businessId: string
  featureModule: OnboardingFeatureModule
  completed: boolean
}

export const ONBOARDING_MODULES: OnboardingFeatureModule[] = [
  'store',
  'orders',
  'finance', 
  'crm',
  'compliance',
  'documents',
  'settings'
]

export const ONBOARDING_STEP_CONFIG: Record<OnboardingFeatureModule, {
  title: string
  description: string
  actionUrl: string
  icon: string
}> = {
  store: {
    title: 'Add your first product or service',
    description: 'Set up your store with products or services to start selling',
    actionUrl: '/store',
    icon: 'Store'
  },
  orders: {
    title: 'Record your first sale or invoice',
    description: 'Create your first order or invoice to track sales',
    actionUrl: '/orders',
    icon: 'ShoppingCart'
  },
  finance: {
    title: 'Set up payment method or record transaction',
    description: 'Connect payment methods or record your first transaction',
    actionUrl: '/finance',
    icon: 'DollarSign'
  },
  crm: {
    title: 'Add your first customer',
    description: 'Build your customer database by adding contacts',
    actionUrl: '/crm',
    icon: 'Users'
  },
  compliance: {
    title: 'Complete a compliance task',
    description: 'Set up regulatory compliance for your business region',
    actionUrl: '/compliance',
    icon: 'FileCheck'
  },
  documents: {
    title: 'Upload a business document',
    description: 'Store important documents like certificates or licenses',
    actionUrl: '/documents',
    icon: 'FileText'
  },
  settings: {
    title: 'Customize your business settings',
    description: 'Set up your business profile, logo, and preferences',
    actionUrl: '/settings',
    icon: 'Settings'
  }
}
