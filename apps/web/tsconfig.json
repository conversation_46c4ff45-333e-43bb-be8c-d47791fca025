{
  "extends": "../../tsconfig.json",
  "compilerOptions": {
    "jsxImportSource": "nativewind",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
  },
  "include": ["next-env.d.ts", "nativewind-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": [
    "node_modules",
    "**/__tests__/**",
    "**/__test__/**",
    "**/*.test.ts",
    "**/*.test.tsx",
    "**/*.spec.ts",
    "**/*.spec.tsx",
    "**/jest.setup.ts",
    "**/jest.setup.js",
    "**/jest.config.ts",
    "**/jest.config.js",
    ".next",
    "dist",
    "build"
  ]
}
