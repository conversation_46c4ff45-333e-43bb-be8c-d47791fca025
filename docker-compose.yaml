services:
  postgres:
    image: postgres:latest
    container_name: ventureDirection-postgres-db
    environment:
      POSTGRES_USER: ventureDirectionUser
      POSTGRES_PASSWORD: ventureDirectionPassword
      POSTGRES_DB: ventureDirection
    ports:
      - "${DATABASE_PORT:-5432}:5432"
    volumes:
      - ven-postgres-data:/var/lib/postgresql/data
    env_file:
      - .env
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ventureDirectionUser -d ventureDirection"]
      interval: 10s
      timeout: 5s
      retries: 5

  ventureDirection:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ventureDirection
    ports:
      - "${APP_PORT:-3000}:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=******************************************************************************************/ventureDirection
    env_file:
      - .env
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - .:/app
      - /app/node_modules
    command: >
      sh -c "until pg_isready -h ventureDirection-postgres-db -p 5432 -U ventureDirectionUser -d ventureDirection; do sleep 1; done &&
           npm run prisma:push &&
           npm run prisma:seed &&
           npm run dev"

  adminer:
    image: adminer
    restart: always
    ports:
      - 8080:8080
    environment:
      ADMINER_DEFAULT_SERVER: ventureDirection-postgres-db

volumes:
  ven-postgres-data: