export interface VentureUserRole {
  id: string
  title: string
  description?: string
  permissions: string[]
  scope: {
    type: 'BUSINESS' | 'BUSINESS_GROUP' | 'GLOBAL'
    entityId?: string
    entityName?: string
  }
  isActive: boolean
  assignedAt: Date
}

export interface VentureUserPermission {
  id: string
  name: string
  description?: string
  resource: string
  action: string
  scope: {
    type: 'BUSINESS' | 'BUSINESS_GROUP' | 'GLOBAL'
    entityId?: string
  }
}

export interface VentureUserBusinessContext {
  businessId: string
  businessName: string
  businessType?: string
  businessGroupId?: string
  businessGroupName?: string
  userRole: string
  permissions: string[]
  isActive: boolean
  lastAccessedAt?: Date
}

export interface VentureUser {
  // Core user data
  id: string
  clerkUserId?: string
  workosUserId?: string
  email: string
  firstName?: string
  lastName?: string
  displayName: string
  avatar?: string
  
  // Account status
  isActive: boolean
  emailVerified: boolean
  createdAt: Date
  updatedAt: Date
  lastLoginAt?: Date
  
  // Current business context (from session)
  currentBusinessId?: string
  currentBusinessGroupId?: string
  
  // Business memberships
  businessMemberships: VentureUserBusinessContext[]
  
  // Roles & permissions for current context
  currentRoles: VentureUserRole[]
  currentPermissions: VentureUserPermission[]
  
  // Global roles (if any)
  globalRoles: VentureUserRole[]
  
  // Computed properties
  hasBusinessAccess: boolean
  isBusinessOwner: boolean
  isBusinessAdmin: boolean
  canManageUsers: boolean
  canManageSettings: boolean
}

export interface VentureUserContextData {
  user: VentureUser | null
  isLoading: boolean
  error: string | null
  refetch: () => Promise<void>
  
  // Permission helpers
  hasPermission: (permission: string, businessId?: string) => boolean
  hasRole: (role: string, businessId?: string) => boolean
  canAccess: (resource: string, action: string, businessId?: string) => boolean
  
  // Business context helpers
  getCurrentBusinessRole: () => string | null
  getCurrentBusinessPermissions: () => string[]
  isOwnerOfBusiness: (businessId: string) => boolean
  isAdminOfBusiness: (businessId: string) => boolean
}

// Server-side user resolution
export interface ResolveUserOptions {
  includeRoles?: boolean
  includePermissions?: boolean
  includeBusinessContext?: boolean
  businessId?: string
  businessGroupId?: string
}

export interface CreateVentureUserData {
  clerkUserId?: string
  workosUserId?: string
  email: string
  firstName?: string
  lastName?: string
  avatar?: string
  emailVerified?: boolean
}

// Re-export from session types for convenience
export interface ClerkSessionMetadata {
  activeBusinessId?: string
  activeBusinessGroupId?: string
  accessibleBusinessIds?: string[]
  accessibleBusinessGroupIds?: string[]
  lastSwitchedAt?: string
}
