import { ComplianceApi } from '@/packages/lib/api/compliance-api'
import { apiClient } from '@/packages/lib/api/client'
import { API_ENDPOINTS } from '@/packages/lib/api/endpoints'

// Mock the API client
jest.mock('@/packages/lib/api/client', () => ({
  apiClient: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    patch: jest.fn(),
    delete: jest.fn(),
    updateBaseURL: jest.fn(),
    setAuthToken: jest.fn()
  }
}))

const mockedApiClient = apiClient as jest.Mocked<typeof apiClient>

describe('ComplianceApi', () => {
  let complianceApi: ComplianceApi

  beforeEach(() => {
    jest.clearAllMocks()
    complianceApi = new ComplianceApi()
  })

  describe('getCompliance', () => {
    it('should fetch compliance without filters', async () => {
      const mockCompliance = [
        {
          id: 'comp-1',
          title: 'Annual Tax Filing',
          description: 'Submit annual tax returns',
          status: 'PENDING',
          priority: 'HIGH',
          dueDate: '2024-12-31',
          businessId: 'business-123'
        },
        {
          id: 'comp-2',
          title: 'Business License Renewal',
          description: 'Renew business operating license',
          status: 'COMPLETED',
          priority: 'MEDIUM',
          dueDate: '2024-06-30',
          businessId: 'business-456'
        }
      ]

      mockedApiClient.get.mockResolvedValue(mockCompliance)

      const result = await complianceApi.getCompliance()

      expect(mockedApiClient.get).toHaveBeenCalledWith(API_ENDPOINTS.COMPLIANCE)
      expect(result).toEqual(mockCompliance)
      expect(result).toHaveLength(2)
    })

    it('should fetch compliance with business filter', async () => {
      const filters = { businessId: 'business-123' }
      const mockCompliance = [
        {
          id: 'comp-1',
          title: 'Tax Filing',
          status: 'PENDING',
          businessId: 'business-123'
        }
      ]

      mockedApiClient.get.mockResolvedValue(mockCompliance)

      const result = await complianceApi.getCompliance(filters)

      expect(mockedApiClient.get).toHaveBeenCalledWith(
        `${API_ENDPOINTS.COMPLIANCE}?businessId=business-123`
      )
      expect(result).toEqual(mockCompliance)
    })

    it('should fetch compliance with multiple filters', async () => {
      const filters = {
        businessId: 'business-123',
        status: 'PENDING',
        dueDate: '2024-12-31'
      }
      const mockCompliance = [
        {
          id: 'comp-1',
          title: 'Tax Filing',
          status: 'PENDING',
          businessId: 'business-123',
          dueDate: '2024-12-31'
        }
      ]

      mockedApiClient.get.mockResolvedValue(mockCompliance)

      const result = await complianceApi.getCompliance(filters)

      const expectedUrl = `${API_ENDPOINTS.COMPLIANCE}?businessId=business-123&status=PENDING&dueDate=2024-12-31`
      expect(mockedApiClient.get).toHaveBeenCalledWith(expectedUrl)
      expect(result).toEqual(mockCompliance)
    })

    it('should handle empty compliance list', async () => {
      mockedApiClient.get.mockResolvedValue([])

      const result = await complianceApi.getCompliance()

      expect(result).toEqual([])
      expect(result).toHaveLength(0)
    })
  })

  describe('getComplianceById', () => {
    it('should fetch compliance by ID successfully', async () => {
      const complianceId = 'comp-123'
      const mockCompliance = {
        id: complianceId,
        title: 'Tax Filing',
        description: 'Annual tax filing requirement',
        status: 'PENDING',
        priority: 'HIGH',
        dueDate: '2024-12-31',
        businessId: 'business-123',
        createdAt: '2024-01-01',
        updatedAt: '2024-01-15'
      }

      mockedApiClient.get.mockResolvedValue(mockCompliance)

      const result = await complianceApi.getComplianceById(complianceId)

      expect(mockedApiClient.get).toHaveBeenCalledWith(API_ENDPOINTS.COMPLIANCE_BY_ID(complianceId))
      expect(result).toEqual(mockCompliance)
      expect(result.id).toBe(complianceId)
    })

    it('should handle compliance not found', async () => {
      const complianceId = 'non-existent'
      const notFoundError = {
        response: {
          status: 404,
          data: { message: 'Compliance requirement not found' }
        }
      }

      mockedApiClient.get.mockRejectedValue(notFoundError)

      await expect(complianceApi.getComplianceById(complianceId)).rejects.toEqual(notFoundError)
    })
  })

  describe('createCompliance', () => {
    it('should create compliance successfully', async () => {
      const createData = {
        title: 'New Compliance Requirement',
        description: 'A new compliance requirement',
        businessId: 'business-123',
        dueDate: '2024-12-31',
        priority: 'MEDIUM'
      }

      const mockCreatedCompliance = {
        id: 'new-comp-id',
        ...createData,
        status: 'PENDING',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      mockedApiClient.post.mockResolvedValue(mockCreatedCompliance)

      const result = await complianceApi.createCompliance(createData)

      expect(mockedApiClient.post).toHaveBeenCalledWith(API_ENDPOINTS.COMPLIANCE, createData)
      expect(result).toEqual(mockCreatedCompliance)
      expect(result.title).toBe(createData.title)
    })

    it('should handle validation errors', async () => {
      const invalidData = { title: '' } // Missing required fields
      const validationError = {
        response: {
          status: 400,
          data: {
            message: 'Validation failed',
            errors: ['Title is required', 'Business ID is required', 'Due date is required']
          }
        }
      }

      mockedApiClient.post.mockRejectedValue(validationError)

      await expect(complianceApi.createCompliance(invalidData as any)).rejects.toEqual(validationError)
    })
  })

  describe('updateCompliance', () => {
    it('should update compliance successfully', async () => {
      const complianceId = 'comp-123'
      const updateData = {
        status: 'COMPLETED',
        notes: 'Task completed successfully on time',
        completedAt: new Date().toISOString()
      }

      const mockUpdatedCompliance = {
        id: complianceId,
        title: 'Tax Filing',
        ...updateData,
        updatedAt: new Date().toISOString()
      }

      mockedApiClient.put.mockResolvedValue(mockUpdatedCompliance)

      const result = await complianceApi.updateCompliance(complianceId, updateData)

      expect(mockedApiClient.put).toHaveBeenCalledWith(
        API_ENDPOINTS.COMPLIANCE_BY_ID(complianceId),
        updateData
      )
      expect(result).toEqual(mockUpdatedCompliance)
      expect(result.status).toBe('COMPLETED')
    })

    it('should handle update permission errors', async () => {
      const complianceId = 'comp-123'
      const updateData = { status: 'COMPLETED' }
      const forbiddenError = {
        response: {
          status: 403,
          data: { message: 'Insufficient permissions to update compliance' }
        }
      }

      mockedApiClient.put.mockRejectedValue(forbiddenError)

      await expect(complianceApi.updateCompliance(complianceId, updateData)).rejects.toEqual(forbiddenError)
    })
  })

  describe('deleteCompliance', () => {
    it('should delete compliance successfully', async () => {
      const complianceId = 'comp-123'
      mockedApiClient.delete.mockResolvedValue(undefined)

      await complianceApi.deleteCompliance(complianceId)

      expect(mockedApiClient.delete).toHaveBeenCalledWith(API_ENDPOINTS.COMPLIANCE_BY_ID(complianceId))
    })

    it('should handle delete errors', async () => {
      const complianceId = 'comp-123'
      const deleteError = {
        response: {
          status: 500,
          data: { message: 'Failed to delete compliance requirement' }
        }
      }

      mockedApiClient.delete.mockRejectedValue(deleteError)

      await expect(complianceApi.deleteCompliance(complianceId)).rejects.toEqual(deleteError)
    })
  })

  describe('getComplianceTemplates', () => {
    it('should fetch all compliance templates', async () => {
      const mockTemplates = [
        {
          id: 'template-1',
          name: 'Annual Tax Filing Template',
          description: 'Template for annual tax filing requirements',
          countryId: 'US',
          category: 'TAX'
        },
        {
          id: 'template-2',
          name: 'Business License Template',
          description: 'Template for business license requirements',
          countryId: 'US',
          category: 'LICENSE'
        }
      ]

      mockedApiClient.get.mockResolvedValue(mockTemplates)

      const result = await complianceApi.getComplianceTemplates()

      expect(mockedApiClient.get).toHaveBeenCalledWith(API_ENDPOINTS.COMPLIANCE_TEMPLATES)
      expect(result).toEqual(mockTemplates)
      expect(result).toHaveLength(2)
    })

    it('should fetch templates by country', async () => {
      const countryId = 'CA'
      const mockTemplates = [
        {
          id: 'template-ca-1',
          name: 'Canadian Tax Filing Template',
          countryId: 'CA'
        }
      ]

      mockedApiClient.get.mockResolvedValue(mockTemplates)

      const result = await complianceApi.getComplianceTemplates(countryId)

      expect(mockedApiClient.get).toHaveBeenCalledWith(
        `${API_ENDPOINTS.COMPLIANCE_TEMPLATES}?countryId=${countryId}`
      )
      expect(result).toEqual(mockTemplates)
    })

    it('should handle empty templates list', async () => {
      mockedApiClient.get.mockResolvedValue([])

      const result = await complianceApi.getComplianceTemplates()

      expect(result).toEqual([])
      expect(result).toHaveLength(0)
    })
  })
})
