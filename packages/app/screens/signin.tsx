import { Text, View } from "@/ui/index";
import { authApi } from "@/lib/api";
import { useState } from "react";
import { Link } from "@/ui/link";


export function SignIn() {
  const [token, setToken] = useState<string | null>(null)
  const handleGenerateToken = async () => {
    try {
      const response = await authApi.GenerateSignInToken({} as any)
      setToken(response.data)
    } catch (error) {
      console.error(error)
    }
  }

  return (
    <View className="flex flex-1 justify-center items-center bg-blue-500">
      <Text className="text-base">
        SignIn Screen
      </Text>

      <Text className="text-base">
        This is the signin screen. You can use this screen to sign in to your account.
      </Text>

      <button onClick={handleGenerateToken}>
        Generate Token
      </button>

      {token && (
        <div>
          <Text>Token: {token}</Text>
          <Link href="/auth/signup">
            <Text>Or Signup</Text>
          </Link>
        </div>
      )}
    </View>
  );
}