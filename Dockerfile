# Use an official Node.js image as base
FROM node:22-alpine AS base

# Set working directory
WORKDIR /app

# Install libc6-compat (needed for compatibility)
RUN apk add --no-cache libc6-compat postgresql-client build-base cairo-dev pango-dev jpeg-dev giflib-dev

# Enable Corepack and prepare Yarn
RUN corepack enable && corepack prepare yarn@4.6.0 --activate

# ---- DEPENDENCIES STAGE ----
FROM base AS deps

# Copy package files first (to maximize Docker cache efficiency)
COPY package.json .yarnrc.yml ./
COPY prisma ./prisma
COPY .yarn ./.yarn
COPY yarn.lock package-lock.json* pnpm-lock.yaml* .npmrc* ./

# Set Yarn to use node-modules linker
RUN yarn config set nodeLinker node-modules

# Install dependencies based on available lock file
RUN \
  if [ -f yarn.lock ]; then \
    yarn install --network-concurrency 1; \
  elif [ -f package-lock.json ]; then \
    npm install; \
  elif [ -f pnpm-lock.yaml ]; then \
    corepack enable pnpm && pnpm i --frozen-lockfile --ignore-scripts; \
  else echo "Lockfile not found." && exit 1; \
  fi

# ---- APPLICATION STAGE ----
FROM base AS runner

# Copy dependencies from deps stage
COPY --from=deps /app/node_modules ./node_modules

# Copy the rest of the application files
COPY . .

# Expose the application port
EXPOSE 3000
ENV PORT=3000

# Set Node.js memory optimization flags
ENV NODE_OPTIONS="--max_old_space_size=2048"

# Start the app (we’ll override this in docker-compose.yml)
CMD ["npm", "run", "dev"]