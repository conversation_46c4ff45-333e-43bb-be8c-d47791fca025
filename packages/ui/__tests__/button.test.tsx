/**
 * Button Component Tests
 *
 * Simple tests to verify the Button component works correctly
 * These tests focus on basic functionality rather than complex mocking
 */

import { Button } from '../button'

// Mock nativewind
jest.mock('nativewind', () => ({
  cssInterop: jest.fn(),
}))

// Mock the cn utility
jest.mock('@/ui/utils/cn', () => ({
  cn: (...classes: any[]) => classes.filter(Boolean).join(' ')
}))

// Mock the Text component
jest.mock('@/ui/text', () => ({
  Text: ({ children, ...props }: any) => children
}))

describe('Button Component - Basic Tests', () => {
  it('exists and can be imported', () => {
    expect(Button).toBeDefined()
    expect(typeof Button).toBe('function')
  })

  it('has correct displayName', () => {
    expect(Button.displayName).toBe('Button')
  })

  it('is a React component', () => {
    // Test that it's a valid React component
    expect(Button).toHaveProperty('$$typeof')
  })

  it('accepts props correctly', () => {
    // Test that the component accepts the expected props
    const props = {
      children: 'Test Button',
      variant: 'default' as const,
      className: 'test-class',
      onPress: jest.fn()
    }

    // Should not throw when creating element
    expect(() => {
      const element = Button(props)
      expect(element).toBeDefined()
    }).not.toThrow()
  })

  describe('Component Structure', () => {
    it('handles string children', () => {
      const props = { children: 'Click me' }
      expect(() => Button(props)).not.toThrow()
    })

    it('handles React node children', () => {
      const props = { children: 'Custom content' }
      expect(() => Button(props)).not.toThrow()
    })

    it('handles variant prop', () => {
      const defaultProps = { children: 'Default', variant: 'default' as const }
      const outlineProps = { children: 'Outline', variant: 'outline' as const }

      expect(() => Button(defaultProps)).not.toThrow()
      expect(() => Button(outlineProps)).not.toThrow()
    })

    it('handles className prop', () => {
      const props = { children: 'Styled', className: 'custom-class' }
      expect(() => Button(props)).not.toThrow()
    })

    it('handles textClassName prop', () => {
      const props = { children: 'Text Styled', textClassName: 'text-class' }
      expect(() => Button(props)).not.toThrow()
    })
  })

  describe('Event Handling', () => {
    it('accepts onPress prop', () => {
      const mockOnPress = jest.fn()
      const props = { children: 'Press me', onPress: mockOnPress }

      expect(() => Button(props)).not.toThrow()
    })

    it('accepts other Pressable props', () => {
      const props = {
        children: 'Accessible',
        testID: 'test-button',
        accessibilityLabel: 'Test button',
        disabled: false
      }

      expect(() => Button(props)).not.toThrow()
    })
  })

  describe('Ref Forwarding', () => {
    it('is a forwardRef component', () => {
      // Check if it's wrapped with forwardRef
      expect(Button.$$typeof).toBeDefined()
    })
  })
})
