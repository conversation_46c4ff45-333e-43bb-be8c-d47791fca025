import type { Config } from "tailwindcss"

// Check if we're running in a Next.js environment
const isNext = process.env.NEXT_RUNTIME === 'nodejs';

// Base configuration that works for both Next.js and React Native
const config: Config = {
  darkMode: ["class"],
  content: [
    './pages/**/*.{js,jsx,ts,tsx}',
    './components/**/*.{js,jsx,ts,tsx}',
    './app/**/*.{js,jsx,ts,tsx}',
    './src/**/*.{js,jsx,ts,tsx}',
    './App.{js,jsx,ts,tsx}',
    './App.native.{js,jsx,ts,tsx}',
  ],
  presets: [require("nativewind/preset")],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        // Brand Colors - Blue & White
        venture: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6', // Main brand blue
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
        
        // Success (Green)
        success: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#22c55e',
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
          DEFAULT: '#22c55e',
          foreground: '#ffffff',
        },
        
        // Warning (Amber)
        warning: {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#f59e0b',
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
          DEFAULT: '#f59e0b',
          foreground: '#ffffff',
        },
        
        // Error (Red)
        error: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
          DEFAULT: '#ef4444',
          foreground: '#ffffff',
        },
        
        // Neutral colors
        gray: {
          50: '#f9fafb',
          100: '#f3f4f6',
          200: '#e5e7eb',
          300: '#d1d5db',
          400: '#9ca3af',
          500: '#6b7280',
          600: '#4b5563',
          700: '#374151',
          800: '#1f2937',
          900: '#111827',
        },
        
        // Semantic colors
        primary: {
          DEFAULT: '#3b82f6', // Same as venture-500
          foreground: '#ffffff',
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
        
        destructive: {
          DEFAULT: '#ef4444',
          foreground: '#ffffff',
        },
        
        // Background and surface colors
        background: '#ffffff',
        foreground: '#0f172a',
        
        // Card colors
        card: {
          DEFAULT: '#ffffff',
          foreground: '#0f172a',
        },
        
        // Input colors
        input: {
          DEFAULT: '#e2e8f0',
          foreground: '#0f172a',
        },
        
        // Border colors
        border: '#e2e8f0',
        
        // Ring colors (focus ring)
        ring: '#3b82f6', // venture-500
        
        // Additional semantic colors
        secondary: {
          DEFAULT: '#f1f5f9',
          foreground: '#0f172a',
        },
        
        muted: {
          DEFAULT: '#f8fafc',
          foreground: '#64748b',
        },
        
        accent: {
          DEFAULT: '#eff6ff',
          foreground: '#1e3a8a',
        },
        
        popover: {
          DEFAULT: '#ffffff',
          foreground: '#0f172a',
        },
      },
      
      borderRadius: {
        lg: '0.5rem',
        md: '0.375rem',
        sm: '0.25rem',
        DEFAULT: '0.25rem',
      },
      
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
      },
      
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
};

// For Next.js, we need to add the NativeWind preset
if (!isNext) {
  // @ts-ignore - NativeWind types are not available
  const nativewind = require('nativewind/preset');
  config.presets = [nativewind];
}

export default config;
