# BusinessUserProvider Context

## 📖 Overview

The `BusinessUserProvider` is a React context provider that supplies business-scoped user data and permissions to all child components. It eliminates redundant API calls and provides seamless access to business-specific user context throughout the component tree.

## 🎯 Purpose

- **Business Scoping**: Automatically scopes all user data to a specific business
- **Performance**: Single API call shared across all child components
- **Permission Management**: Pre-computed business-specific permissions
- **Layout Integration**: Designed for business workspace layouts

## 📝 Signature

```typescript
function BusinessUserProvider({
  businessId,
  children,
  fallback?,
  loadingFallback?,
  errorFallback?
}: BusinessUserProviderProps): JSX.Element
```

### Props

```typescript
interface BusinessUserProviderProps {
  businessId: string              // Required: Business ID to scope context to
  children: React.ReactNode       // Child components
  fallback?: React.ReactNode      // Shown when user has no access
  loadingFallback?: React.ReactNode // Shown while loading
  errorFallback?: React.ReactNode   // Shown on error
}
```

## 🚀 Basic Usage

### Simple Business Layout

```typescript
import { BusinessUserProvider } from '@/lib/contexts/business-user-context'

function BusinessPage({ businessId }) {
  return (
    <BusinessUserProvider businessId={businessId}>
      <BusinessHeader />
      <BusinessSidebar />
      <BusinessContent />
    </BusinessUserProvider>
  )
}

// All child components can now use useBusinessUser()
function BusinessHeader() {
  const { businessName, userRole } = useBusinessUser()
  return (
    <header>
      <h1>{businessName}</h1>
      <span>Role: {userRole}</span>
    </header>
  )
}
```

### With Custom Fallbacks

```typescript
function BusinessWorkspace({ businessId }) {
  return (
    <BusinessUserProvider 
      businessId={businessId}
      loadingFallback={<BusinessLoadingSpinner />}
      errorFallback={<BusinessErrorMessage />}
      fallback={<BusinessAccessDenied />}
    >
      <BusinessDashboard />
    </BusinessUserProvider>
  )
}

function BusinessLoadingSpinner() {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <Spinner className="h-8 w-8 mb-4" />
        <p>Loading business workspace...</p>
      </div>
    </div>
  )
}

function BusinessErrorMessage() {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center text-red-600">
        <AlertCircle className="h-12 w-12 mb-4 mx-auto" />
        <h2>Failed to load business</h2>
        <p>Please try again or contact support</p>
      </div>
    </div>
  )
}

function BusinessAccessDenied() {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <Lock className="h-12 w-12 mb-4 mx-auto text-gray-400" />
        <h2>Access Denied</h2>
        <p>You don't have permission to access this business</p>
        <Button className="mt-4">Request Access</Button>
      </div>
    </div>
  )
}
```

## 🏗️ Layout Integration

### Complete Business Layout

```typescript
import { BusinessUserProvider } from '@/lib/contexts/business-user-context'

function BusinessLayout({ businessId, children }) {
  return (
    <BusinessUserProvider businessId={businessId}>
      <div className="min-h-screen bg-gray-50">
        <BusinessHeader />
        <div className="flex">
          <BusinessSidebar />
          <main className="flex-1 ml-64 pt-16">
            <div className="p-6">
              {children}
            </div>
          </main>
        </div>
      </div>
    </BusinessUserProvider>
  )
}

// Usage in Next.js pages
export default function OrdersPage() {
  const { currentBusiness } = useBusiness()
  
  if (!currentBusiness) {
    return <SelectBusinessPrompt />
  }
  
  return (
    <BusinessLayout businessId={currentBusiness.id}>
      <OrderManagement />
    </BusinessLayout>
  )
}
```

### Nested Business Components

```typescript
function BusinessDashboard() {
  return (
    <div className="space-y-6">
      <DashboardHeader />
      <DashboardStats />
      <DashboardWidgets />
    </div>
  )
}

function DashboardHeader() {
  const { businessName, userRole, isOwner } = useBusinessUser()
  
  return (
    <div className="flex justify-between items-center">
      <div>
        <h1 className="text-2xl font-bold">{businessName}</h1>
        <p className="text-gray-600">Welcome back, {userRole}</p>
      </div>
      {isOwner && (
        <Button>Business Settings</Button>
      )}
    </div>
  )
}

function DashboardStats() {
  const { hasPermission } = useBusinessUser()
  
  return (
    <div className="grid grid-cols-4 gap-4">
      {hasPermission('orders.view') && <OrdersStats />}
      {hasPermission('finance.view') && <RevenueStats />}
      {hasPermission('users.view') && <TeamStats />}
      {hasPermission('analytics.view') && <AnalyticsStats />}
    </div>
  )
}

function DashboardWidgets() {
  const { canManageOrders, canViewFinance, isAdmin } = useBusinessUser()
  
  return (
    <div className="grid grid-cols-2 gap-6">
      {canManageOrders && <RecentOrdersWidget />}
      {canViewFinance && <FinancialSummaryWidget />}
      {isAdmin && <AdminNotificationsWidget />}
    </div>
  )
}
```

## 🔄 Dynamic Business Switching

### Handling Business Changes

```typescript
function DynamicBusinessLayout() {
  const { currentBusiness } = useBusiness()
  const [businessId, setBusinessId] = useState(currentBusiness?.id)
  
  // Update business context when current business changes
  useEffect(() => {
    if (currentBusiness?.id !== businessId) {
      setBusinessId(currentBusiness?.id)
    }
  }, [currentBusiness?.id, businessId])
  
  if (!businessId) {
    return <SelectBusinessPrompt />
  }
  
  return (
    <BusinessUserProvider 
      key={businessId} // Force re-mount on business change
      businessId={businessId}
    >
      <BusinessWorkspace />
    </BusinessUserProvider>
  )
}
```

### Business Switcher Integration

```typescript
function BusinessSwitcherLayout() {
  const { currentBusiness, switchBusiness } = useBusiness()
  
  const handleBusinessSwitch = async (newBusinessId: string) => {
    await switchBusiness(newBusinessId)
    // BusinessUserProvider will automatically update context
  }
  
  return (
    <div>
      <BusinessSwitcher onSwitch={handleBusinessSwitch} />
      
      {currentBusiness && (
        <BusinessUserProvider businessId={currentBusiness.id}>
          <BusinessContent />
        </BusinessUserProvider>
      )}
    </div>
  )
}
```

## 🔒 Permission-Based Layouts

### Conditional Layout Elements

```typescript
function AdaptiveBusinessLayout({ businessId, children }) {
  return (
    <BusinessUserProvider businessId={businessId}>
      <BusinessLayoutContent>
        {children}
      </BusinessLayoutContent>
    </BusinessUserProvider>
  )
}

function BusinessLayoutContent({ children }) {
  const { 
    isAdmin, 
    canManageUsers, 
    hasPermission,
    businessName 
  } = useBusinessUser()
  
  return (
    <div className="business-layout">
      <header className="business-header">
        <h1>{businessName}</h1>
        <div className="header-actions">
          {canManageUsers && (
            <Button>Manage Team</Button>
          )}
          {isAdmin && (
            <Button>Settings</Button>
          )}
        </div>
      </header>
      
      <div className="business-body">
        <aside className="business-sidebar">
          <nav>
            <NavItem href="/dashboard">Dashboard</NavItem>
            {hasPermission('orders.view') && (
              <NavItem href="/orders">Orders</NavItem>
            )}
            {hasPermission('finance.view') && (
              <NavItem href="/finance">Finance</NavItem>
            )}
            {hasPermission('analytics.view') && (
              <NavItem href="/analytics">Analytics</NavItem>
            )}
            {isAdmin && (
              <NavItem href="/settings">Settings</NavItem>
            )}
          </nav>
        </aside>
        
        <main className="business-main">
          {children}
        </main>
      </div>
    </div>
  )
}
```

## 🎯 Advanced Patterns

### Multi-Level Permission Checks

```typescript
function AdvancedBusinessFeatures() {
  return (
    <BusinessUserProvider businessId="business-123">
      <FeatureGate />
    </BusinessUserProvider>
  )
}

function FeatureGate() {
  const { 
    hasPermission, 
    isAdmin, 
    isOwner,
    permissions 
  } = useBusinessUser()
  
  // Complex permission logic
  const canAccessAdvancedFeatures = (
    isAdmin || 
    hasPermission('advanced.access') ||
    permissions.includes('beta.features')
  )
  
  const canManageIntegrations = (
    isOwner || 
    (isAdmin && hasPermission('integrations.manage'))
  )
  
  return (
    <div>
      <BasicFeatures />
      
      {canAccessAdvancedFeatures && (
        <AdvancedFeatures />
      )}
      
      {canManageIntegrations && (
        <IntegrationManagement />
      )}
    </div>
  )
}
```

### Error Boundary Integration

```typescript
import { ErrorBoundary } from 'react-error-boundary'

function RobustBusinessProvider({ businessId, children }) {
  return (
    <ErrorBoundary
      FallbackComponent={BusinessErrorFallback}
      onError={(error, errorInfo) => {
        console.error('Business context error:', error, errorInfo)
        // Log to error reporting service
      }}
    >
      <BusinessUserProvider businessId={businessId}>
        {children}
      </BusinessUserProvider>
    </ErrorBoundary>
  )
}

function BusinessErrorFallback({ error, resetErrorBoundary }) {
  return (
    <div className="error-boundary">
      <h2>Something went wrong with the business context</h2>
      <details>
        <summary>Error details</summary>
        <pre>{error.message}</pre>
      </details>
      <Button onClick={resetErrorBoundary}>
        Try Again
      </Button>
    </div>
  )
}
```

## 🚨 Common Pitfalls

### 1. **Missing Business ID**

```typescript
// ❌ Will show fallback - no businessId
<BusinessUserProvider businessId={undefined}>
  <BusinessContent />
</BusinessUserProvider>

// ✅ Ensure businessId is available
const { currentBusiness } = useBusiness()
if (!currentBusiness) return <SelectBusiness />

<BusinessUserProvider businessId={currentBusiness.id}>
  <BusinessContent />
</BusinessUserProvider>
```

### 2. **Nested Providers**

```typescript
// ❌ Nested providers can cause confusion
<BusinessUserProvider businessId="business-1">
  <BusinessUserProvider businessId="business-2">
    <Component /> {/* Which business context? */}
  </BusinessUserProvider>
</BusinessUserProvider>

// ✅ Use single provider per business workspace
<BusinessUserProvider businessId="business-1">
  <BusinessWorkspace />
</BusinessUserProvider>
```

### 3. **Stale Business ID**

```typescript
// ❌ Business ID doesn't update
const [businessId] = useState(initialBusinessId)
<BusinessUserProvider businessId={businessId}>

// ✅ Business ID updates with current business
const { currentBusiness } = useBusiness()
<BusinessUserProvider businessId={currentBusiness?.id}>
```

## 🎯 Best Practices

1. **Use at business workspace layout level**
2. **Provide meaningful fallback components**
3. **Handle business ID changes properly**
4. **Avoid nested providers**
5. **Implement proper error boundaries**
6. **Test permission edge cases**
7. **Document business-specific requirements**
