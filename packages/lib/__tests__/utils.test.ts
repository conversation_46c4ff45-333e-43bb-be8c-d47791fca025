import {
  cn,
  formatDate,
  formatDateTime,
  formatRelativeTime,
  truncate,
  capitalize,
  slugify,
  formatCurrency,
  formatNumber,
  formatBytes,
  isValidEmail,
  isValidPhone,
  isValidUrl,
  groupBy,
  sortBy,
  unique,
  omit,
  pick,
  sleep,
  debounce,
} from '../utils'

describe('Utils - Class Names', () => {
  it('combines class names correctly', () => {
    expect(cn('class1', 'class2')).toBe('class1 class2')
    expect(cn('class1', undefined, 'class2')).toBe('class1 class2')
    expect(cn('class1', false && 'class2', 'class3')).toBe('class1 class3')
  })
})

describe('Utils - Date Formatting', () => {
  const testDate = new Date('2024-01-15T10:30:00Z')

  it('formats date correctly', () => {
    const formatted = formatDate(testDate)
    expect(formatted).toMatch(/Jan 15, 2024/)
  })

  it('formats date with custom options', () => {
    const formatted = formatDate(testDate, { year: '2-digit' })
    expect(formatted).toMatch(/24/)
  })

  it('formats datetime correctly', () => {
    const formatted = formatDateTime(testDate)
    expect(formatted).toMatch(/Jan 15, 2024/)
    expect(formatted).toMatch(/11:30|10:30/) // Account for timezone differences
  })

  it('formats relative time correctly', () => {
    const now = new Date()
    const oneMinuteAgo = new Date(now.getTime() - 60 * 1000)
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)

    expect(formatRelativeTime(oneMinuteAgo)).toBe('1m ago')
    expect(formatRelativeTime(oneHourAgo)).toBe('1h ago')
    expect(formatRelativeTime(oneDayAgo)).toBe('1d ago')
  })
})

describe('Utils - String Utilities', () => {
  it('truncates strings correctly', () => {
    expect(truncate('Hello World', 5)).toBe('Hello...')
    expect(truncate('Hi', 5)).toBe('Hi')
  })

  it('capitalizes strings correctly', () => {
    expect(capitalize('hello world')).toBe('Hello world')
    expect(capitalize('HELLO')).toBe('Hello')
  })

  it('creates slugs correctly', () => {
    expect(slugify('Hello World!')).toBe('hello-world')
    expect(slugify('Test & Example')).toBe('test-example')
    expect(slugify('  Multiple   Spaces  ')).toBe('multiple-spaces')
  })
})

describe('Utils - Number Formatting', () => {
  it('formats currency correctly', () => {
    expect(formatCurrency(1234.56)).toBe('$1,234.56')
    expect(formatCurrency(1000, 'EUR')).toMatch(/1,000/)
  })

  it('formats numbers correctly', () => {
    expect(formatNumber(1234567)).toBe('1,234,567')
  })

  it('formats bytes correctly', () => {
    expect(formatBytes(0)).toBe('0 Bytes')
    expect(formatBytes(1024)).toBe('1 KB')
    expect(formatBytes(1024 * 1024)).toBe('1 MB')
    expect(formatBytes(1024 * 1024 * 1024)).toBe('1 GB')
  })
})

describe('Utils - Validation', () => {
  it('validates emails correctly', () => {
    expect(isValidEmail('<EMAIL>')).toBe(true)
    expect(isValidEmail('invalid-email')).toBe(false)
    expect(isValidEmail('test@')).toBe(false)
  })

  it('validates phone numbers correctly', () => {
    expect(isValidPhone('+1234567890')).toBe(true)
    expect(isValidPhone('(*************')).toBe(true)
    expect(isValidPhone('123')).toBe(false)
  })

  it('validates URLs correctly', () => {
    expect(isValidUrl('https://example.com')).toBe(true)
    expect(isValidUrl('http://test.org')).toBe(true)
    expect(isValidUrl('invalid-url')).toBe(false)
  })
})

describe('Utils - Array Utilities', () => {
  const testData = [
    { name: 'John', age: 30, category: 'A' },
    { name: 'Jane', age: 25, category: 'B' },
    { name: 'Bob', age: 35, category: 'A' },
  ]

  it('groups by key correctly', () => {
    const grouped = groupBy(testData, 'category')
    expect(grouped.A).toHaveLength(2)
    expect(grouped.B).toHaveLength(1)
  })

  it('sorts by key correctly', () => {
    const sorted = sortBy(testData, 'age')
    expect(sorted[0].age).toBe(25)
    expect(sorted[2].age).toBe(35)

    const sortedDesc = sortBy(testData, 'age', 'desc')
    expect(sortedDesc[0].age).toBe(35)
    expect(sortedDesc[2].age).toBe(25)
  })

  it('returns unique values', () => {
    const duplicates = [1, 2, 2, 3, 3, 3]
    expect(unique(duplicates)).toEqual([1, 2, 3])
  })
})

describe('Utils - Object Utilities', () => {
  const testObj = { a: 1, b: 2, c: 3, d: 4 }

  it('omits keys correctly', () => {
    const result = omit(testObj, ['b', 'd'])
    expect(result).toEqual({ a: 1, c: 3 })
  })

  it('picks keys correctly', () => {
    const result = pick(testObj, ['a', 'c'])
    expect(result).toEqual({ a: 1, c: 3 })
  })
})

describe('Utils - Async Utilities', () => {
  it('sleep function works', async () => {
    const start = Date.now()
    await sleep(100)
    const end = Date.now()
    expect(end - start).toBeGreaterThanOrEqual(90) // Allow some tolerance
  })

  it('debounce function works', (done) => {
    const mockFn = jest.fn()
    const debouncedFn = debounce(mockFn, 100)

    debouncedFn()
    debouncedFn()
    debouncedFn()

    // Should not be called immediately
    expect(mockFn).not.toHaveBeenCalled()

    setTimeout(() => {
      expect(mockFn).toHaveBeenCalledTimes(1)
      done()
    }, 150)
  })
})
