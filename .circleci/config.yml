version: 2.1

orbs:
  node: circleci/node@7.1.0

commands:
  extract-pr-info:
    description: Extract pull request and commit info using curl (works for both feature and main branches)
    parameters:
      is_main_branch:
        type: boolean
        default: false
    steps:
      - run:
          name: Extract Pull Request and Commit Info
          command: |
            # Initialize variables
            PR_NUMBER="unknown"
            PR_TITLE="unknown"
            PR_AUTHOR="unknown"
            BRANCH_NAME="$CIRCLE_BRANCH"
            LAST_COMMITTER_NAME="unknown"
            LAST_COMMITTER_GITHUB="unknown"
            
            # Get commit author info first (works for both main and feature branches)
            LAST_COMMITTER_NAME=$(git log -1 --pretty=format:'%an' || echo "unknown")
            
            # Get GitHub username for commit author
            COMMIT_RESPONSE=$(curl -s -w "%{http_code}" -o commit.json \
              -H "Authorization: Bearer $NPM_TOKEN" \
              -H "Accept: application/vnd.github+json" \
              "https://api.github.com/repos/$CIRCLE_PROJECT_USERNAME/$CIRCLE_PROJECT_REPONAME/commits/$CIRCLE_SHA1")
            
            if [ "$COMMIT_RESPONSE" = "200" ]; then
              LAST_COMMITTER_GITHUB=$(jq -r '.author.login // "unknown"' commit.json)
            else
              echo "Warning: Failed to fetch commit info (HTTP $COMMIT_RESPONSE)"
            fi
            
            # Branch-specific logic for PR info extraction
            if [ "<< parameters.is_main_branch >>" = "true" ]; then
              echo "Processing PR info for main branch: $CIRCLE_BRANCH"
              
              # For main branch: Find the merged PR by matching merge_commit_sha
              PR_NUMBER=$(curl -s -L \
                -H "Accept: application/vnd.github+json" \
                -H "Authorization: Bearer $NPM_TOKEN" \
                "https://api.github.com/repos/$CIRCLE_PROJECT_USERNAME/$CIRCLE_PROJECT_REPONAME/pulls?state=closed&base=main&sort=updated&direction=desc" \
                | jq '[.[] | select(.merged_at != null) | select(.merge_commit_sha == "'"$CIRCLE_SHA1"'")] | first | .number' \
                | tr -d '"')
              
              if [ -z "$PR_NUMBER" ] || [ "$PR_NUMBER" = "null" ]; then
                echo "No matching merged PR found for commit $CIRCLE_SHA1"
                PR_NUMBER="unknown"
              else
                echo "Found merged PR #$PR_NUMBER for commit $CIRCLE_SHA1"
                
                # Get PR details
                PR_RESPONSE=$(curl -s -w "%{http_code}" -o pr.json \
                  -H "Authorization: Bearer $NPM_TOKEN" \
                  -H "Accept: application/vnd.github+json" \
                  "https://api.github.com/repos/$CIRCLE_PROJECT_USERNAME/$CIRCLE_PROJECT_REPONAME/pulls/$PR_NUMBER")
                
                if [ "$PR_RESPONSE" = "200" ]; then
                  { read -r PR_TITLE; read -r BRANCH_NAME; read -r PR_AUTHOR; } < <(
                    jq -r '.title // "unknown", .head.ref // "unknown", .user.login // "unknown"' pr.json
                  )
                else
                  echo "Warning: Failed to fetch PR details (HTTP $PR_RESPONSE)"
                fi
              fi
              
            else
              echo "Processing PR info for feature branch: $CIRCLE_BRANCH"
              
              # For feature branches: Extract PR number from CIRCLE_PULL_REQUEST
              if [ -n "$CIRCLE_PULL_REQUEST" ]; then
                PR_NUMBER=$(echo "$CIRCLE_PULL_REQUEST" | grep -oE '[0-9]+$' || echo "unknown")
                
                if [ "$PR_NUMBER" != "unknown" ]; then
                  echo "Found PR #$PR_NUMBER from CIRCLE_PULL_REQUEST"
                  
                  # Get PR details
                  PR_RESPONSE=$(curl -s -w "%{http_code}" -o pr.json \
                    -H "Authorization: Bearer $NPM_TOKEN" \
                    -H "Accept: application/vnd.github+json" \
                    "https://api.github.com/repos/$CIRCLE_PROJECT_USERNAME/$CIRCLE_PROJECT_REPONAME/pulls/$PR_NUMBER")
                  
                  if [ "$PR_RESPONSE" = "200" ]; then
                    { read -r PR_TITLE; read -r PR_AUTHOR; } < <(
                      jq -r '.title // "unknown", .user.login // "unknown"' pr.json
                    )
                  else
                    echo "Warning: Failed to fetch PR details (HTTP $PR_RESPONSE)"
                  fi
                else
                  echo "Could not extract PR number from CIRCLE_PULL_REQUEST: $CIRCLE_PULL_REQUEST"
                fi
              else
                echo "No CIRCLE_PULL_REQUEST found - this might be a direct push"
                PR_NUMBER="0"
              fi
            fi
            
            # Export all variables to BASH_ENV
            echo "export PR_NUMBER=\"$PR_NUMBER\"" >> "$BASH_ENV"
            echo "export PR_TITLE=\"$PR_TITLE\"" >> "$BASH_ENV"
            echo "export PR_AUTHOR=\"$PR_AUTHOR\"" >> "$BASH_ENV"
            echo "export BRANCH_NAME=\"$BRANCH_NAME\"" >> "$BASH_ENV"
            echo "export LAST_COMMITTER_NAME=\"$LAST_COMMITTER_NAME\"" >> "$BASH_ENV"
            echo "export LAST_COMMITTER_GITHUB=\"$LAST_COMMITTER_GITHUB\"" >> "$BASH_ENV"
            
            # Source the environment
            source "$BASH_ENV"
            
            # Debug output
            echo "=== Extracted PR and Commit Info ==="
            echo "CIRCLE_PULL_REQUEST: $CIRCLE_PULL_REQUEST"
            echo "PR_NUMBER: $PR_NUMBER"
            echo "PR_TITLE: $PR_TITLE"
            echo "PR_AUTHOR: $PR_AUTHOR"
            echo "BRANCH_NAME: $BRANCH_NAME"
            echo "LAST_COMMITTER_NAME: $LAST_COMMITTER_NAME"
            echo "LAST_COMMITTER_GITHUB: $LAST_COMMITTER_GITHUB"
            echo "CIRCLE_BRANCH: $CIRCLE_BRANCH"
            echo "CIRCLE_SHA1: $CIRCLE_SHA1"
            echo "===================================="
            
  setup-node:
    description: Install Node dependencies and cache
    steps:
      - restore_cache:
          keys:
            - yarn-deps-{{ checksum "yarn.lock" }}-v2
      - node/install-packages:
          pkg-manager: yarn
      - save_cache:
          paths:
            - ~/.yarn/cache
            - node_modules
          key: yarn-deps-{{ checksum "yarn.lock" }}-v2

  setup-vercel:
    description: Install and cache Vercel CLI
    steps:
      - restore_cache:
          keys:
            - vercel-cli-{{ .Environment.CIRCLECI_CACHE_VERSION }}-v2
      - run:
          name: Install Vercel CLI
          command: |
            if ! command -v vercel >/dev/null 2>&1; then
              yarn global add vercel@latest
            fi
      - save_cache:
          key: vercel-cli-{{ .Environment.CIRCLECI_CACHE_VERSION }}-v2
          paths:
            - ~/.yarn/global/node_modules/vercel
            - ~/.yarn/bin/vercel

  wait-for-postgres:
    description: Ensure PostgreSQL is ready
    steps:
      - run:
          name: Wait for PostgreSQL
          command: |
            for i in {1..10}; do
              if nc -z localhost 5432; then
                echo "PostgreSQL is ready!"
                exit 0
              fi
              echo "Waiting for PostgreSQL... Attempt $i/10"
              sleep 3
            done
            echo "PostgreSQL failed to start"
            exit 1

jobs:
  check-pr:
    docker:
      - image: cimg/node:lts
    steps:
      - checkout
      - run:
          name: Install required tools
          command: |
            sudo apt-get update
            sudo apt-get install -y jq
      - extract-pr-info:
          is_main_branch: false

  run-tests:
    docker:
      - image: cimg/node:lts
      - image: postgres:13
        environment:
          POSTGRES_DB: venturedirection
          POSTGRES_USER: ventureDirectionUser
          POSTGRES_PASSWORD: ventureDirectionPassword
    parameters:
      is_main_branch:
        type: boolean
        default: false
    steps:
      - checkout
      - run:
          name: Enable Corepack 
          command: sudo corepack enable 
      - run:
          name: Install required tools
          command: |
            sudo apt-get update
            sudo apt-get install -y jq
      - extract-pr-info:
          is_main_branch: << parameters.is_main_branch >>
      - setup-node
      - wait-for-postgres
      - run:
          name: Set Environment
          command: |
            echo 'export DATABASE_URL=postgres://ventureDirectionUser:ventureDirectionPassword@localhost:5432/venturedirection' >> $BASH_ENV
            echo 'export NODE_ENV=CI' >> $BASH_ENV
            echo 'export POSTGRES_DATABASE_URL=postgres://ventureDirectionUser:ventureDirectionPassword@localhost:5432/venturedirection' >> $BASH_ENV
            echo 'export POSTGRES_DIRECT_URL=postgres://ventureDirectionUser:ventureDirectionPassword@localhost:5432/venturedirection' >> $BASH_ENV
      - run:
          name: Setup and Seed Database with Prisma
          command: |
            yarn prisma:generate
            echo "Database migration and seeding are not yet implemented"
            yarn prisma:push
      - run:
          name: Run Unit Tests
          command: |
            yarn test:api && yarn test:services 2> >(tee unit_test_failure.txt)
      - run:
          name: Notify on Test Failure
          command: |
            if [ -n "$NOTIFICATION_URL" ]; then
              message="failed-pipeline"
              failure_reason=$(cat unit_test_failure.txt 2>/dev/null || echo "Unit test failure")
              payload=$(jq -n \
                --arg username "$LAST_COMMITTER_GITHUB" \
                --arg message "$message" \
                --arg CIRCLE_PULL_REQUEST "$CIRCLE_PULL_REQUEST" \
                --arg pr_number "$PR_NUMBER" \
                --arg pr_title "$PR_TITLE" \
                --arg pr_author "$PR_AUTHOR" \
                --arg last_committer "$LAST_COMMITTER_NAME" \
                --arg failure_reason "$failure_reason" \
                --arg CIRCLE_BRANCH "$CIRCLE_BRANCH" \
                --arg CIRCLE_PROJECT_REPONAME "$CIRCLE_PROJECT_REPONAME" \
                --arg CIRCLE_PROJECT_USERNAME "$CIRCLE_PROJECT_USERNAME" \
                --arg CIRCLE_REPOSITORY_URL "$CIRCLE_REPOSITORY_URL" \
                --arg CIRCLE_SHA1 "$CIRCLE_SHA1" \
                '{username: $username, message: $message, vars: {CIRCLE_PULL_REQUEST: $CIRCLE_PULL_REQUEST, PR_TITLE: $pr_title, PR_NUMBER: $pr_number, PR_AUTHOR: $pr_author, LAST_COMMITTER_NAME: $last_committer, LAST_COMMITTER_GITHUB: $username, CIRCLE_BRANCH: $CIRCLE_BRANCH, CIRCLE_PROJECT_REPONAME: $CIRCLE_PROJECT_REPONAME, CIRCLE_PROJECT_USERNAME: $CIRCLE_PROJECT_USERNAME, CIRCLE_REPOSITORY_URL: $CIRCLE_REPOSITORY_URL, CIRCLE_SHA1: $CIRCLE_SHA1, failure_reason: $failure_reason}}')
              echo "Sending failure notification: $payload"
              curl -X POST -H 'Content-Type: application/json' --data "$payload" "$NOTIFICATION_URL"
            else
              echo "No failure webhook URL configured."
            fi
          when: on_fail

  preview-deployment:
    docker:
      - image: cimg/node:lts
    steps:
      - checkout
      - run:
          name: Install required tools
          command: |
            sudo apt-get update
            sudo apt-get install -y jq
      - extract-pr-info:
          is_main_branch: false
      - setup-node
      - setup-vercel
      - run:
          name: Generate Prisma Client
          command: yarn prisma:generate
      - run:
          name: Build All Packages
          command: yarn build
      - run:
          name: Pull Vercel Environment
          command: |
            cd apps/web
            npx vercel pull --yes --environment=preview --token=$VERCEL_TOKEN
      - run:
          name: Load Vercel Env Vars
          command: |
            cd apps/web
            source .vercel/.env.preview.local
      - run:
          name: Build Preview
          command: |
            cd apps/web
            npx vercel build --token=$VERCEL_TOKEN 2> >(tee build_failure.txt)
      - run:
          name: Notify on Build Failure
          command: |
            if [ -n "$NOTIFICATION_URL" ]; then
              message="failed-pipeline"
              failure_reason=$(cat build_failure.txt 2>/dev/null || echo "Build failure")
              payload=$(jq -n \
                --arg username "$LAST_COMMITTER_GITHUB" \
                --arg message "$message" \
                --arg CIRCLE_PULL_REQUEST "$CIRCLE_PULL_REQUEST" \
                --arg pr_number "$PR_NUMBER" \
                --arg pr_title "$PR_TITLE" \
                --arg pr_author "$PR_AUTHOR" \
                --arg last_committer "$LAST_COMMITTER_NAME" \
                --arg failure_reason "$failure_reason" \
                --arg CIRCLE_BRANCH "$CIRCLE_BRANCH" \
                --arg CIRCLE_PROJECT_REPONAME "$CIRCLE_PROJECT_REPONAME" \
                --arg CIRCLE_PROJECT_USERNAME "$CIRCLE_PROJECT_USERNAME" \
                --arg CIRCLE_REPOSITORY_URL "$CIRCLE_REPOSITORY_URL" \
                --arg CIRCLE_SHA1 "$CIRCLE_SHA1" \
                '{username: $username, message: $message, vars: {CIRCLE_PULL_REQUEST: $CIRCLE_PULL_REQUEST, PR_TITLE: $pr_title, PR_NUMBER: $pr_number, PR_AUTHOR: $pr_author, LAST_COMMITTER_NAME: $last_committer, LAST_COMMITTER_GITHUB: $username, CIRCLE_BRANCH: $CIRCLE_BRANCH, CIRCLE_PROJECT_REPONAME: $CIRCLE_PROJECT_REPONAME, CIRCLE_PROJECT_USERNAME: $CIRCLE_PROJECT_USERNAME, CIRCLE_REPOSITORY_URL: $CIRCLE_REPOSITORY_URL, CIRCLE_SHA1: $CIRCLE_SHA1, failure_reason: $failure_reason}}')
              echo "Sending failure notification: $payload"
              curl -X POST -H 'Content-Type: application/json' --data "$payload" "$NOTIFICATION_URL"
            else
              echo "No notification webhook URL configured."
            fi
          when: on_fail
      - run:
          name: Deploy Preview
          command: |
            cd apps/web
            preview_url=$(npx vercel deploy --prebuilt --token=$VERCEL_TOKEN --archive=tgz)
            echo "export PREVIEW_URL=$preview_url" >> $BASH_ENV
      - run:
          name: Notify Preview Deployment
          command: |
            JSON_PAYLOAD=$(jq -n \
              --arg pr_number "$PR_NUMBER" \
              --arg preview_url "$PREVIEW_URL" \
              --arg branch_name "$BRANCH_NAME" \
              --arg repo_name "$CIRCLE_PROJECT_REPONAME" \
              --arg repo_owner "$CIRCLE_PROJECT_USERNAME" \
              --arg author "$LAST_COMMITTER_GITHUB" \
              --arg pr_author "$PR_AUTHOR" \
              '{pr_number: $pr_number, preview_url: $preview_url, branch_name: $branch_name, repo_name: $repo_name, repo_owner: $repo_owner, author: $author, pr_author: $pr_author}')
            echo "Preview Deployment Payload: $JSON_PAYLOAD"
            if [ "$PR_NUMBER" != "0" ] && [ "$PR_NUMBER" != "unknown" ]; then
              curl -X POST "$NOTIFICATION_URL" -H "Content-Type: application/json" -d "$JSON_PAYLOAD"
            else
              echo "Skipping notification - no valid PR number"
            fi

  production-deployment:
    docker:
      - image: cimg/node:lts
    steps:
      - checkout
      - run:
          name: Skip Dependabot Commits
          command: |
            AUTHOR=$(git log -1 --pretty=format:'%ae')
            if [[ "$AUTHOR" == "dependabot[bot]@users.noreply.github.com" ]]; then
              echo "Skipping production deployment for Dependabot"
              circleci-agent step halt
            fi
      - run:
          name: Install required tools
          command: |
            sudo apt-get update
            sudo apt-get install -y jq
      - extract-pr-info:
          is_main_branch: true
      - setup-node
      - setup-vercel
      - run:
          name: Generate Prisma Client
          command: yarn prisma:generate
      - run:
          name: Build All Packages
          command: yarn build
      - run:
          name: Pull Vercel Production Env
          command: |
            cd apps/web
            npx vercel pull --yes --environment=production --token=$VERCEL_TOKEN
      - run:
          name: Load Vercel Env Vars
          command: |
            cd apps/web
            source .vercel/.env.production.local
      - run:
          name: Build Production
          command: |
            cd apps/web
            npx vercel build --prod --token=$VERCEL_TOKEN
      - run:
          name: Deploy Production
          command: |
            cd apps/web
            production_url=$(npx vercel deploy --prebuilt --prod --token=$VERCEL_TOKEN --archive=tgz)
            echo "Production deployment: $production_url"
            # Optional: Send production deployment notification
            JSON_PAYLOAD=$(jq -n \
              --arg pr_number "$PR_NUMBER" \
              --arg production_url "$production_url" \
              --arg branch_name "$BRANCH_NAME" \
              --arg repo_name "$CIRCLE_PROJECT_REPONAME" \
              --arg repo_owner "$CIRCLE_PROJECT_USERNAME" \
              --arg author "$LAST_COMMITTER_GITHUB" \
              --arg pr_author "$PR_AUTHOR" \
              '{pr_number: $pr_number, production_url: $production_url, branch_name: $branch_name, repo_name: $repo_name, repo_owner: $repo_owner, author: $author, pr_author: $pr_author, deployment_type: "production"}')
            echo "Production Deployment Payload: $JSON_PAYLOAD"

workflows:
  ci-cd:
    jobs:
      - check-pr:
          filters:
            branches:
              ignore: main
      - run-tests:
          name: Run Tests (Feature)
          is_main_branch: false
          requires:
            - check-pr
          filters:
            branches:
              ignore: main
      - run-tests:
          name: Run Tests (Main)
          is_main_branch: true
          filters:
            branches:
              only: main
      - preview-deployment:
          requires:
            - Run Tests (Feature)
          filters:
            branches:
              ignore:
                - main
                - /^dependabot\/.*/
      - production-deployment:
          requires:
            - Run Tests (Main)
          filters:
            branches:
              only: main