import { AuthApi } from '@/packages/lib/api/auth-api'
import { apiClient } from '@/packages/lib/api/client'
import { API_ENDPOINTS } from '@/packages/lib/api/endpoints'

// Mock the API client
jest.mock('@/packages/lib/api/client', () => ({
  apiClient: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    patch: jest.fn(),
    delete: jest.fn(),
    updateBaseURL: jest.fn(),
    setAuthToken: jest.fn()
  }
}))

const mockedApiClient = apiClient as jest.Mocked<typeof apiClient>

describe('AuthApi', () => {
  let authApi: AuthApi

  beforeEach(() => {
    jest.clearAllMocks()
    authApi = new AuthApi()
  })

  describe('GenerateSignInToken', () => {
    it('should generate sign-in token successfully', async () => {
      const signInData = {
        email: '<EMAIL>',
        password: 'securePassword123'
      }

      const mockResponse = {
        success: true,
        data: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        message: 'Sign-in token generated successfully',
        expiresIn: 3600
      }

      mockedApiClient.post.mockResolvedValue(mockResponse)

      const result = await authApi.GenerateSignInToken(signInData)

      expect(mockedApiClient.post).toHaveBeenCalledWith(API_ENDPOINTS.SIGNINTOKEN, signInData)
      expect(result).toEqual(mockResponse)
      expect(result.success).toBe(true)
      expect(result.data).toBeTruthy()
    })

    it('should handle invalid credentials', async () => {
      const signInData = {
        email: '<EMAIL>',
        password: 'wrongPassword'
      }

      const authError = {
        response: {
          status: 401,
          data: {
            success: false,
            message: 'Invalid email or password',
            error: 'INVALID_CREDENTIALS'
          }
        }
      }

      mockedApiClient.post.mockRejectedValue(authError)

      await expect(authApi.GenerateSignInToken(signInData)).rejects.toEqual(authError)
      expect(mockedApiClient.post).toHaveBeenCalledWith(API_ENDPOINTS.SIGNINTOKEN, signInData)
    })

    it('should handle validation errors', async () => {
      const invalidData = {
        email: 'invalid-email',
        password: ''
      }

      const validationError = {
        response: {
          status: 400,
          data: {
            success: false,
            message: 'Validation failed',
            errors: [
              'Email must be a valid email address',
              'Password is required'
            ]
          }
        }
      }

      mockedApiClient.post.mockRejectedValue(validationError)

      await expect(authApi.GenerateSignInToken(invalidData)).rejects.toEqual(validationError)
    })

    it('should handle account locked error', async () => {
      const signInData = {
        email: '<EMAIL>',
        password: 'password123'
      }

      const lockedError = {
        response: {
          status: 423,
          data: {
            success: false,
            message: 'Account is temporarily locked due to multiple failed login attempts',
            error: 'ACCOUNT_LOCKED',
            lockoutExpiresAt: new Date(Date.now() + 30 * 60 * 1000).toISOString()
          }
        }
      }

      mockedApiClient.post.mockRejectedValue(lockedError)

      await expect(authApi.GenerateSignInToken(signInData)).rejects.toEqual(lockedError)
    })
  })

  describe('SignOut', () => {
    it('should sign out user successfully', async () => {
      const userId = 'user-123'
      const mockResponse = {
        success: true,
        data: null,
        message: 'User signed out successfully'
      }

      mockedApiClient.post.mockResolvedValue(mockResponse)

      const result = await authApi.SignOut(userId)

      expect(mockedApiClient.post).toHaveBeenCalledWith(API_ENDPOINTS.SIGNOUT, userId)
      expect(result).toEqual(mockResponse)
      expect(result.success).toBe(true)
    })

    it('should handle invalid user ID', async () => {
      const userId = 'invalid-user'
      const notFoundError = {
        response: {
          status: 404,
          data: {
            success: false,
            message: 'User not found',
            error: 'USER_NOT_FOUND'
          }
        }
      }

      mockedApiClient.post.mockRejectedValue(notFoundError)

      await expect(authApi.SignOut(userId)).rejects.toEqual(notFoundError)
    })

    it('should handle already signed out user', async () => {
      const userId = 'user-123'
      const alreadySignedOutError = {
        response: {
          status: 400,
          data: {
            success: false,
            message: 'User is already signed out',
            error: 'ALREADY_SIGNED_OUT'
          }
        }
      }

      mockedApiClient.post.mockRejectedValue(alreadySignedOutError)

      await expect(authApi.SignOut(userId)).rejects.toEqual(alreadySignedOutError)
    })
  })

  describe('CheckAuth', () => {
    it('should check authentication successfully', async () => {
      const mockResponse = {
        success: true,
        data: {
          id: 'user-123',
          clerkUserId: 'clerk-456',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '+1234567890',
          language: 'en',
          timezone: 'UTC',
          activeBusinessId: 'business-789',
          activeBusinessGroupId: 'group-101',
          accessibleBusinessIds: ['business-789', 'business-202'],
          accessibleBusinessGroupIds: ['group-101'],
          emailAddresses: ['<EMAIL>'],
          userSettings: {
            theme: 'dark',
            notifications: true
          },
          roles: [
            {
              id: 'role-1',
              name: 'Business Owner',
              scope: 'BUSINESS'
            }
          ],
          permissions: [
            {
              id: 'perm-1',
              name: 'manage_business',
              resource: 'business'
            }
          ]
        },
        message: 'User authenticated successfully'
      }

      mockedApiClient.post.mockResolvedValue(mockResponse)

      const result = await authApi.CheckAuth()

      expect(mockedApiClient.post).toHaveBeenCalledWith(API_ENDPOINTS.CHECKAUTH)
      expect(result).toEqual(mockResponse)
      expect(result.success).toBe(true)
      expect(result.data.id).toBe('user-123')
    })

    it('should handle unauthenticated user', async () => {
      const unauthenticatedError = {
        response: {
          status: 401,
          data: {
            success: false,
            message: 'User is not authenticated',
            error: 'UNAUTHENTICATED'
          }
        }
      }

      mockedApiClient.post.mockRejectedValue(unauthenticatedError)

      await expect(authApi.CheckAuth()).rejects.toEqual(unauthenticatedError)
    })

    it('should handle expired session', async () => {
      const expiredSessionError = {
        response: {
          status: 401,
          data: {
            success: false,
            message: 'Session has expired',
            error: 'SESSION_EXPIRED'
          }
        }
      }

      mockedApiClient.post.mockRejectedValue(expiredSessionError)

      await expect(authApi.CheckAuth()).rejects.toEqual(expiredSessionError)
    })

    it('should handle partial user data', async () => {
      const mockResponse = {
        success: true,
        data: {
          id: 'user-123',
          clerkUserId: 'clerk-456',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: null,
          language: 'en',
          timezone: 'UTC',
          activeBusinessId: null,
          activeBusinessGroupId: null,
          accessibleBusinessIds: [],
          accessibleBusinessGroupIds: [],
          emailAddresses: ['<EMAIL>'],
          userSettings: {},
          roles: [],
          permissions: []
        },
        message: 'User authenticated successfully'
      }

      mockedApiClient.post.mockResolvedValue(mockResponse)

      const result = await authApi.CheckAuth()

      expect(result).toEqual(mockResponse)
      expect(result.data.activeBusinessId).toBeNull()
      expect(result.data.roles).toHaveLength(0)
    })
  })

  describe('Error handling', () => {
    it('should handle network errors', async () => {
      const networkError = new Error('Network connection failed')
      networkError.name = 'NetworkError'

      mockedApiClient.post.mockRejectedValue(networkError)

      await expect(authApi.CheckAuth()).rejects.toThrow('Network connection failed')
    })

    it('should handle timeout errors', async () => {
      const timeoutError = {
        code: 'ECONNABORTED',
        message: 'Request timeout of 10000ms exceeded'
      }

      mockedApiClient.post.mockRejectedValue(timeoutError)

      await expect(authApi.CheckAuth()).rejects.toEqual(timeoutError)
    })

    it('should handle server errors', async () => {
      const serverError = {
        response: {
          status: 500,
          data: {
            success: false,
            message: 'Internal server error',
            error: 'INTERNAL_ERROR'
          }
        }
      }

      mockedApiClient.post.mockRejectedValue(serverError)

      await expect(authApi.CheckAuth()).rejects.toEqual(serverError)
    })
  })
})
