# VentureUser Hook & Helper Examples

## 🎯 Basic Usage

### Client Components

```typescript
import { useVentureUser } from '@/lib/hooks/useVentureUser'

function MyComponent() {
  const { user, isLoading, hasPermission, hasRole } = useVentureUser()
  
  if (isLoading) return <div>Loading...</div>
  if (!user) return <div>Not authenticated</div>
  
  return (
    <div>
      <h1>Welcome, {user.displayName}!</h1>
      <p>Email: {user.email}</p>
      
      {hasPermission('users.manage') && (
        <button>Manage Users</button>
      )}
      
      {hasRole('admin') && (
        <button>Admin Panel</button>
      )}
    </div>
  )
}
```

### Server Components

```typescript
import { getVentureUser } from '@/lib/services/venture-user-service'

export default async function ServerComponent() {
  const user = await getVentureUser({
    includeRoles: true,
    includePermissions: true
  })
  
  if (!user) {
    return <div>Please sign in</div>
  }
  
  return (
    <div>
      <h1>Server-side: {user.displayName}</h1>
      <p>Business: {user.currentBusinessId}</p>
      <p>Roles: {user.currentRoles.map(r => r.title).join(', ')}</p>
    </div>
  )
}
```

## 🔒 Permission Guards

### Component-level Protection

```typescript
import { PermissionGuard, RoleGuard, AdminGuard } from '@/components/auth/permissionGuard'

function ProtectedFeatures() {
  return (
    <div>
      {/* Permission-based access */}
      <PermissionGuard permission="users.manage">
        <UserManagementPanel />
      </PermissionGuard>
      
      {/* Role-based access */}
      <RoleGuard role="admin">
        <AdminDashboard />
      </RoleGuard>
      
      {/* Admin access (any admin role) */}
      <AdminGuard>
        <AdminSettings />
      </AdminGuard>
      
      {/* Resource + Action based */}
      <PermissionGuard resource="finance" action="view">
        <FinancialReports />
      </PermissionGuard>
      
      {/* Business-specific permissions */}
      <PermissionGuard permission="orders.manage" businessId="business-123">
        <OrderManagement />
      </PermissionGuard>
    </div>
  )
}
```

### Custom Fallbacks

```typescript
<PermissionGuard 
  permission="admin.access"
  fallback={
    <div className="text-center p-8">
      <h3>Upgrade Required</h3>
      <p>Contact sales to access admin features</p>
      <Button>Contact Sales</Button>
    </div>
  }
>
  <AdminPanel />
</PermissionGuard>
```

## 🏢 Business Context

### Current Business Data

```typescript
function BusinessContextExample() {
  const { user, getCurrentBusinessRole, getCurrentBusinessPermissions } = useVentureUser()
  
  const currentRole = getCurrentBusinessRole()
  const permissions = getCurrentBusinessPermissions()
  
  return (
    <div>
      <h2>Current Business: {user?.currentBusinessId}</h2>
      <p>Your Role: {currentRole}</p>
      <p>Permissions: {permissions.join(', ')}</p>
      
      {user?.businessMemberships.map(membership => (
        <div key={membership.businessId}>
          <h3>{membership.businessName}</h3>
          <p>Role: {membership.userRole}</p>
          <p>Group: {membership.businessGroupName}</p>
        </div>
      ))}
    </div>
  )
}
```

### Cross-Business Checks

```typescript
function CrossBusinessExample() {
  const { isOwnerOfBusiness, isAdminOfBusiness } = useVentureUser()
  
  const businessId = 'business-123'
  
  return (
    <div>
      {isOwnerOfBusiness(businessId) && (
        <button>Transfer Ownership</button>
      )}
      
      {isAdminOfBusiness(businessId) && (
        <button>Manage Business</button>
      )}
    </div>
  )
}
```

## ⚡ Performance Optimizations

### Caching

```typescript
// Hook automatically caches for 5 minutes
const { user } = useVentureUser({
  cacheTimeout: 10 * 60 * 1000 // 10 minutes
})

// Force refresh (bypasses cache)
const { refetch } = useVentureUser()
await refetch()
```

### Selective Loading

```typescript
// Basic user data only (faster)
const { user } = useVentureUserBasic()

// Full context with roles and permissions
const { user } = useVentureUser({
  includeRoles: true,
  includePermissions: true,
  includeBusinessContext: true
})

// Specific business context
const { user } = useVentureUser({
  businessId: 'specific-business-id'
})
```

### Specialized Hooks

```typescript
// Check single permission
const { hasPermission } = usePermission('users.manage')

// Check single role
const { hasRole } = useRole('admin')
```

## 🔄 Real-world Examples

### Navigation Menu

```typescript
function NavigationMenu() {
  const { user, hasPermission } = useVentureUser()
  
  return (
    <nav>
      <Link href="/dashboard">Dashboard</Link>
      
      {hasPermission('users.view') && (
        <Link href="/users">Users</Link>
      )}
      
      {hasPermission('finance.view') && (
        <Link href="/finance">Finance</Link>
      )}
      
      {user?.isBusinessAdmin && (
        <Link href="/admin">Admin</Link>
      )}
    </nav>
  )
}
```

### User Profile Display

```typescript
import { VentureUserProfile } from '@/components/user/ventureUserProfile'

function ProfilePage() {
  return (
    <div>
      <VentureUserProfile 
        showBusinessContext={true}
        showPermissions={true}
        showActions={true}
      />
    </div>
  )
}
```

### API Route Protection

```typescript
// app/api/admin/route.ts
import { getVentureUser } from '@/lib/services/venture-user-service'

export async function GET() {
  const user = await getVentureUser({ includeRoles: true })
  
  if (!user?.isBusinessAdmin) {
    return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
  }
  
  // Admin logic here
  return NextResponse.json({ data: 'admin data' })
}
```

## 🎯 Benefits

### 1. **Provider Agnostic**
- Works with Clerk or WorkOS
- Easy to switch auth providers
- Internal user model stays consistent

### 2. **Business Context Aware**
- Automatic business scoping
- Role/permission per business
- Cross-business access checks

### 3. **Performance Optimized**
- Client-side caching
- Selective data loading
- Minimal API calls

### 4. **Developer Friendly**
- Simple hook interface
- TypeScript support
- Comprehensive guards

### 5. **Secure by Default**
- Server-side validation
- Permission-based access
- Business context isolation
