import { BusinessService } from '../business-service'
import { prisma } from '@/apps/web/db'

// Mock Prisma completely
jest.mock('@/apps/web/db', () => ({
  prisma: {
    business: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      upsert: jest.fn(),
      count: jest.fn(),
    },
    userBusinessMembership: {
      findFirst: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      upsert: jest.fn(),
    },
    country: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
    },
    businessGroup: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
    },
    $transaction: jest.fn(),
    $connect: jest.fn(),
    $disconnect: jest.fn(),
  }
}))

const mockedPrisma = prisma as jest.Mocked<typeof prisma>

describe('BusinessService', () => {
  let businessService: BusinessService

  beforeEach(() => {
    jest.clearAllMocks()
    businessService = new BusinessService()
  })

  describe('getUserBusinesses', () => {
    it('should return user businesses with proper access control', async () => {
      const userId = 'user-123'
      const mockMemberships = [
        {
          businessId: 'business-1',
          business: {
            id: 'business-1',
            name: 'Business One',
            status: 'ACTIVE',
            country: { name: 'United States' }
          }
        },
        {
          businessId: 'business-2',
          business: {
            id: 'business-2',
            name: 'Business Two',
            status: 'ACTIVE',
            country: { name: 'Canada' }
          }
        }
      ]

      mockedPrisma.userBusinessMembership.findMany.mockResolvedValue(mockMemberships as any)

      const result = await businessService.getUserBusinesses(userId)

      expect(mockedPrisma.userBusinessMembership.findMany).toHaveBeenCalledWith({
        where: {
          userId,
          isActive: true,
        },
        include: {
          business: {
            include: {
              country: true,
              businessIndustries: {
                include: {
                  industry: true
                }
              },
              _count: {
                select: {
                  compliance: true,
                  notes: true,
                  secureVaults: true
                }
              }
            }
          }
        },
        orderBy: {
          lastAccessedAt: 'desc'
        }
      })

      expect(result).toEqual(mockMemberships.map(m => m.business))
    })

    it('should return empty array when user has no businesses', async () => {
      const userId = 'user-no-business'
      mockedPrisma.userBusinessMembership.findMany.mockResolvedValue([])

      const result = await businessService.getUserBusinesses(userId)

      expect(result).toEqual([])
    })

    it('should handle database errors', async () => {
      const userId = 'user-123'
      const dbError = new Error('Database connection failed')
      mockedPrisma.userBusinessMembership.findMany.mockRejectedValue(dbError)

      await expect(businessService.getUserBusinesses(userId)).rejects.toThrow('Database connection failed')
    })
  })

  describe('getBusinessById', () => {
    it('should return business by ID with access control', async () => {
      const businessId = 'business-123'
      const userId = 'user-123'
      const mockBusiness = {
        id: businessId,
        name: 'Test Business',
        status: 'ACTIVE',
        country: { name: 'United States' }
      }

      // Mock access check
      mockedPrisma.userBusinessMembership.findFirst.mockResolvedValue({ businessId, userId } as any)
      mockedPrisma.business.findUnique.mockResolvedValue(mockBusiness as any)

      const result = await businessService.getBusinessById(businessId, userId)

      expect(mockedPrisma.userBusinessMembership.findFirst).toHaveBeenCalledWith({
        where: {
          userId,
          businessId,
          isActive: true
        }
      })

      expect(mockedPrisma.business.findUnique).toHaveBeenCalledWith({
        where: { id: businessId },
        include: {
          country: true,
          businessIndustries: {
            include: {
              industry: true
            }
          },
          compliance: {
            where: {
              status: { in: ['PENDING', 'IN_PROGRESS'] }
            },
            orderBy: { dueDate: 'asc' },
            take: 5
          },
          _count: {
            select: {
              compliance: true,
              notes: true,
              secureVaults: true
            }
          }
        }
      })

      expect(result).toEqual(mockBusiness)
    })

    it('should throw error when user has no access to business', async () => {
      const businessId = 'business-123'
      const userId = 'user-no-access'

      mockedPrisma.userBusinessMembership.findFirst.mockResolvedValue(null)

      await expect(businessService.getBusinessById(businessId, userId))
        .rejects.toThrow('Access denied: User does not have access to this business')
    })

    it('should return business without access control when no userId provided', async () => {
      const businessId = 'business-123'
      const mockBusiness = {
        id: businessId,
        name: 'Public Business',
        status: 'ACTIVE'
      }

      mockedPrisma.business.findUnique.mockResolvedValue(mockBusiness as any)

      const result = await businessService.getBusinessById(businessId)

      expect(mockedPrisma.userBusinessMembership.findFirst).not.toHaveBeenCalled()
      expect(result).toEqual(mockBusiness)
    })
  })

  describe('createBusiness', () => {
    it('should create business and assign owner membership', async () => {
      const userId = 'user-123'
      const businessData = {
        name: 'New Business',
        countryId: 'US',
        registrationNumber: '123456789'
      }
      const mockCreatedBusiness = {
        id: 'new-business-id',
        ...businessData,
        status: 'PENDING'
      }

      mockedPrisma.$transaction.mockImplementation(async (callback) => {
        return await callback(mockedPrisma)
      })

      mockedPrisma.business.create.mockResolvedValue(mockCreatedBusiness as any)

      const result = await businessService.createBusiness(userId, businessData)

      expect(mockedPrisma.$transaction).toHaveBeenCalled()
      expect(result).toEqual(mockCreatedBusiness)
    })

    it('should handle validation errors', async () => {
      const userId = 'user-123'
      const invalidData = { name: '' } // Missing required fields

      const validationError = new Error('Validation failed')
      mockedPrisma.$transaction.mockRejectedValue(validationError)

      await expect(businessService.createBusiness(userId, invalidData as any))
        .rejects.toThrow('Validation failed')
    })
  })

  describe('checkUserBusinessAccess', () => {
    it('should return true when user has access', async () => {
      const userId = 'user-123'
      const businessId = 'business-123'

      mockedPrisma.userBusinessMembership.findFirst.mockResolvedValue({
        userId,
        businessId,
        isActive: true
      } as any)

      const result = await businessService.checkUserBusinessAccess(userId, businessId)

      expect(result).toBe(true)
      expect(mockedPrisma.userBusinessMembership.findFirst).toHaveBeenCalledWith({
        where: {
          userId,
          businessId,
          isActive: true
        }
      })
    })

    it('should return false when user has no access', async () => {
      const userId = 'user-no-access'
      const businessId = 'business-123'

      mockedPrisma.userBusinessMembership.findFirst.mockResolvedValue(null)

      const result = await businessService.checkUserBusinessAccess(userId, businessId)

      expect(result).toBe(false)
    })
  })
})
