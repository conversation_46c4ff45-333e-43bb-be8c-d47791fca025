/**
 * Root Jest Setup
 *
 * This setup is for package-level testing (packages/lib, packages/ui, packages/app).
 * Includes React Native component testing support.
 * For web app testing (API & services), see apps/web/jest.setup.ts
 */

import '@testing-library/jest-native/extend-expect'

// Mock environment variables for packages
Object.defineProperty(process.env, 'NODE_ENV', { value: 'test' })

// Mock React Native modules
jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native')

  return {
    ...RN,
    Platform: {
      ...RN.Platform,
      OS: 'ios',
      select: jest.fn((obj) => obj.ios || obj.default),
    },
    Dimensions: {
      get: jest.fn(() => ({ width: 375, height: 812 })),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    },
  }
})

// Mock nativewind
jest.mock('nativewind', () => ({
  cssInterop: jest.fn(),
  styled: jest.fn(() => (component: any) => component),
}))

// Setup test timeout
jest.setTimeout(10000)

// Clear all mocks before each test
beforeEach(() => {
  jest.clearAllMocks()
})

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})