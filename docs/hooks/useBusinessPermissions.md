# useBusinessPermissions Hook

## 📖 Overview

The `useBusinessPermissions` hook provides comprehensive business-scoped permission checks and role validation. It offers pre-computed permission helpers for common business operations and can only be used within a `BusinessUserProvider`.

## 🎯 Purpose

- **Business-Scoped Permissions**: All checks automatically scoped to current business
- **Pre-Computed Helpers**: Common permission patterns pre-calculated
- **Role-Based Access**: Owner, Admin, Member role checks
- **Feature Access**: Module-specific permission validation

## ⚠️ Requirements

**Must be used within `BusinessUserProvider`:**

```typescript
<BusinessUserProvider businessId="business-123">
  <ComponentUsingBusinessPermissions /> {/* ✅ Works */}
</BusinessUserProvider>

<ComponentUsingBusinessPermissions /> {/* ❌ Throws error */}
```

## 📝 Signature

```typescript
function useBusinessPermissions(): BusinessPermissionsReturn
```

### Return Value

```typescript
interface BusinessPermissionsReturn {
  // Core permission checks
  hasPermission: (permission: string) => boolean
  hasRole: (role: string) => boolean
  canAccess: (resource: string, action: string) => boolean
  
  // Role checks
  isOwner: boolean
  isAdmin: boolean
  
  // User management permissions
  canViewUsers: boolean
  canManageUsers: boolean
  canInviteUsers: boolean
  canRemoveUsers: boolean
  
  // Business settings permissions
  canViewSettings: boolean
  canManageSettings: boolean
  canManageBusiness: boolean
  
  // Financial permissions
  canViewFinance: boolean
  canManageFinance: boolean
  canCreateInvoices: boolean
  canManagePayments: boolean
  
  // Orders & Inventory permissions
  canViewOrders: boolean
  canManageOrders: boolean
  canViewInventory: boolean
  canManageInventory: boolean
  
  // CRM permissions
  canViewCRM: boolean
  canManageCRM: boolean
  canManageCustomers: boolean
  
  // Documents & Compliance permissions
  canViewDocuments: boolean
  canManageDocuments: boolean
  canViewCompliance: boolean
  canManageCompliance: boolean
  
  // Analytics & Reports permissions
  canViewAnalytics: boolean
  canViewReports: boolean
  canExportReports: boolean
}
```

## 🚀 Basic Usage

### User Management Features

```typescript
import { useBusinessPermissions } from '@/lib/hooks/useBusinessPermissions'

function UserManagement() {
  const {
    canViewUsers,
    canManageUsers,
    canInviteUsers,
    canRemoveUsers,
    isOwner,
    isAdmin
  } = useBusinessPermissions()
  
  if (!canViewUsers) {
    return <AccessDenied feature="User Management" />
  }
  
  return (
    <div>
      <h1>Team Management</h1>
      
      <UserList />
      
      <div className="actions">
        {canInviteUsers && (
          <Button>Invite User</Button>
        )}
        
        {canManageUsers && (
          <Button>Manage Roles</Button>
        )}
        
        {canRemoveUsers && (
          <Button variant="destructive">Remove Users</Button>
        )}
        
        {isOwner && (
          <Button>Transfer Ownership</Button>
        )}
      </div>
    </div>
  )
}
```

### Financial Dashboard

```typescript
function FinancialDashboard() {
  const {
    canViewFinance,
    canManageFinance,
    canCreateInvoices,
    canManagePayments,
    isAdmin
  } = useBusinessPermissions()
  
  if (!canViewFinance) {
    return <AccessDenied feature="Finance" />
  }
  
  return (
    <div>
      <h1>Financial Management</h1>
      
      <FinancialOverview />
      
      {canCreateInvoices && (
        <div className="invoice-section">
          <h2>Invoices</h2>
          <Button>Create Invoice</Button>
          <InvoiceList />
        </div>
      )}
      
      {canManagePayments && (
        <div className="payments-section">
          <h2>Payments</h2>
          <PaymentManagement />
        </div>
      )}
      
      {canManageFinance && (
        <div className="finance-settings">
          <h2>Financial Settings</h2>
          <FinanceSettings />
        </div>
      )}
      
      {isAdmin && (
        <div className="admin-finance">
          <h2>Admin Controls</h2>
          <FinanceAdminPanel />
        </div>
      )}
    </div>
  )
}
```

### Order Management

```typescript
function OrderManagement() {
  const {
    canViewOrders,
    canManageOrders,
    canViewInventory,
    canManageInventory,
    hasPermission
  } = useBusinessPermissions()
  
  if (!canViewOrders) {
    return <AccessDenied feature="Orders" />
  }
  
  return (
    <div>
      <h1>Order Management</h1>
      
      <OrderList />
      
      {canManageOrders && (
        <div className="order-actions">
          <Button>Create Order</Button>
          <Button>Bulk Actions</Button>
          
          {hasPermission('orders.export') && (
            <Button>Export Orders</Button>
          )}
        </div>
      )}
      
      {canViewInventory && (
        <div className="inventory-section">
          <h2>Inventory</h2>
          <InventoryOverview />
          
          {canManageInventory && (
            <InventoryManagement />
          )}
        </div>
      )}
    </div>
  )
}
```

## 🔒 Permission Patterns

### Navigation with Permissions

```typescript
function BusinessNavigation() {
  const {
    canViewOrders,
    canViewFinance,
    canViewCRM,
    canViewAnalytics,
    canViewSettings,
    isAdmin,
    isOwner
  } = useBusinessPermissions()
  
  const navigationItems = [
    { name: 'Dashboard', href: '/dashboard', show: true },
    { name: 'Orders', href: '/orders', show: canViewOrders },
    { name: 'Finance', href: '/finance', show: canViewFinance },
    { name: 'CRM', href: '/crm', show: canViewCRM },
    { name: 'Analytics', href: '/analytics', show: canViewAnalytics },
    { name: 'Settings', href: '/settings', show: canViewSettings },
    { name: 'Admin', href: '/admin', show: isAdmin },
    { name: 'Business Groups', href: '/groups', show: isOwner },
  ]
  
  return (
    <nav>
      {navigationItems
        .filter(item => item.show)
        .map(item => (
          <NavItem key={item.name} href={item.href}>
            {item.name}
          </NavItem>
        ))
      }
    </nav>
  )
}
```

### Feature Access Control

```typescript
function BusinessFeatures() {
  const {
    canViewFinance,
    canManageUsers,
    canViewAnalytics,
    canManageSettings,
    isOwner
  } = useBusinessPermissions()
  
  return (
    <div className="feature-grid">
      {/* Financial features */}
      {canViewFinance && (
        <FeatureCard
          title="Finance"
          description="Manage invoices and payments"
          href="/finance"
        />
      )}
      
      {/* User management */}
      {canManageUsers && (
        <FeatureCard
          title="Team Management"
          description="Invite and manage team members"
          href="/users"
        />
      )}
      
      {/* Analytics */}
      {canViewAnalytics && (
        <FeatureCard
          title="Analytics"
          description="Business insights and reports"
          href="/analytics"
        />
      )}
      
      {/* Settings */}
      {canManageSettings && (
        <FeatureCard
          title="Settings"
          description="Configure business preferences"
          href="/settings"
        />
      )}
      
      {/* Owner-only features */}
      {isOwner && (
        <FeatureCard
          title="Business Groups"
          description="Manage business relationships"
          href="/groups"
          badge="Owner Only"
        />
      )}
    </div>
  )
}
```

### Conditional UI Elements

```typescript
function BusinessHeader() {
  const {
    canManageUsers,
    canManageSettings,
    isAdmin,
    isOwner
  } = useBusinessPermissions()
  
  return (
    <header className="business-header">
      <div className="header-content">
        <BusinessInfo />
        
        <div className="header-actions">
          {canManageUsers && (
            <Button variant="outline">
              <Users className="h-4 w-4 mr-2" />
              Manage Team
            </Button>
          )}
          
          {canManageSettings && (
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          )}
          
          {isAdmin && (
            <Button variant="outline">
              <Shield className="h-4 w-4 mr-2" />
              Admin Panel
            </Button>
          )}
          
          {isOwner && (
            <Button variant="outline">
              <Crown className="h-4 w-4 mr-2" />
              Owner Controls
            </Button>
          )}
        </div>
      </div>
    </header>
  )
}
```

## 🎣 Related Hooks

### useBusinessRole

Check specific business role:

```typescript
import { useBusinessRole } from '@/lib/hooks/useBusinessPermissions'

function OwnerOnlyFeature() {
  const isOwner = useBusinessRole('OWNER')
  
  if (!isOwner) return null
  
  return <OwnershipTransfer />
}
```

### useIsBusinessOwner

Direct owner check:

```typescript
import { useIsBusinessOwner } from '@/lib/hooks/useBusinessPermissions'

function OwnerBadge() {
  const isOwner = useIsBusinessOwner()
  
  return isOwner ? (
    <Badge variant="warning">
      <Crown className="h-3 w-3 mr-1" />
      Owner
    </Badge>
  ) : null
}
```

### useIsBusinessAdmin

Admin check (includes owners):

```typescript
import { useIsBusinessAdmin } from '@/lib/hooks/useBusinessPermissions'

function AdminFeatures() {
  const isAdmin = useIsBusinessAdmin()
  
  return isAdmin ? <AdminPanel /> : <MemberView />
}
```

### useBusinessContext

Business context information:

```typescript
import { useBusinessContext } from '@/lib/hooks/useBusinessPermissions'

function BusinessInfo() {
  const {
    businessName,
    userRole,
    permissions,
    hasAnyPermissions
  } = useBusinessContext()
  
  return (
    <div>
      <h2>{businessName}</h2>
      <p>Role: {userRole}</p>
      <p>Permissions: {permissions.length}</p>
      
      {!hasAnyPermissions && (
        <Alert>No permissions assigned. Contact administrator.</Alert>
      )}
    </div>
  )
}
```

### useBusinessFeatureAccess

Feature-level access checks:

```typescript
import { useBusinessFeatureAccess } from '@/lib/hooks/useBusinessPermissions'

function FeatureAccessExample() {
  const {
    canAccessStore,
    canAccessFinance,
    canAccessAdminPanel,
    canManageAnyFeature
  } = useBusinessFeatureAccess()
  
  return (
    <div>
      {canAccessStore && <StoreFeatures />}
      {canAccessFinance && <FinanceFeatures />}
      {canAccessAdminPanel && <AdminFeatures />}
      
      {!canManageAnyFeature && (
        <div>You have view-only access to this business.</div>
      )}
    </div>
  )
}
```

## 🚨 Common Pitfalls

### 1. **Using Outside BusinessUserProvider**

```typescript
// ❌ Will throw error
function MyComponent() {
  const { canManageUsers } = useBusinessPermissions() // Error!
}

// ✅ Correct usage
<BusinessUserProvider businessId="123">
  <MyComponent /> {/* Now it works */}
</BusinessUserProvider>
```

### 2. **Permission vs Role Confusion**

```typescript
// ❌ Checking role when permission is more appropriate
const { hasRole } = useBusinessPermissions()
const canEdit = hasRole('admin') // Too broad

// ✅ Check specific permission
const { hasPermission } = useBusinessPermissions()
const canEdit = hasPermission('orders.edit') // More specific
```

### 3. **Assuming Permission Hierarchy**

```typescript
// ❌ Assuming admin has all permissions
const { isAdmin } = useBusinessPermissions()
if (isAdmin) {
  // Assume can do anything - may not be true
}

// ✅ Check specific permissions
const { canManageFinance, isAdmin } = useBusinessPermissions()
if (canManageFinance || isAdmin) {
  // More explicit permission check
}
```

## 🎯 Best Practices

1. **Use within BusinessUserProvider context**
2. **Prefer specific permissions over broad role checks**
3. **Combine role and permission checks when appropriate**
4. **Handle permission denial gracefully**
5. **Use pre-computed helpers for common patterns**
6. **Test permission boundaries thoroughly**
7. **Document custom permission requirements**
