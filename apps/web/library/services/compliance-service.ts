import { prisma } from '@/apps/web/db'

interface ComplianceFilters {
  businessId?: string
  status?: string
  dueDate?: string
  priority?: string
}

// interface CreateComplianceData {
//   title: string
//   description?: string
//   businessId: string
//   dueDate: Date
//   priority?: string
// }

export class ComplianceService {
  async getCompliance(filters: ComplianceFilters = {}, userId?: string): Promise<any> {
    // If businessId filter is provided, check user access
    if (filters.businessId && userId) {
      const hasAccess = await this.checkUserBusinessAccess(userId, filters.businessId)
      if (!hasAccess) {
        throw new Error('Access denied: User does not have access to this business')
      }
    }

    // Build where clause
    let whereClause: any = {}

    if (filters.businessId) {
      whereClause.businessId = filters.businessId
    } else if (userId) {
      // Get all accessible businesses for user
      const accessibleBusinessIds = await this.getUserAccessibleBusinesses(userId)
      whereClause.businessId = { in: accessibleBusinessIds }
    }

    if (filters.status) {
      whereClause.status = filters.status
    }

    if (filters.dueDate) {
      whereClause.dueDate = { lte: new Date(filters.dueDate) }
    }

    if (filters.priority) {
      whereClause.priority = filters.priority
    }

    return await prisma.compliance.findMany({
      where: whereClause,
      include: {
        business: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        dueDate: 'asc'
      }
    })
  }

  async getComplianceById(complianceId: string, userId?: string): Promise<any> {
    const compliance = await prisma.compliance.findUnique({
      where: { id: complianceId },
      include: {
        business: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    if (!compliance) {
      throw new Error('Compliance requirement not found')
    }

    // Check user access if userId provided
    if (userId) {
      const hasAccess = await this.checkUserBusinessAccess(userId, compliance.businessId)
      if (!hasAccess) {
        throw new Error('Access denied: User does not have access to this compliance requirement')
      }
    }

    return compliance
  }

  async createCompliance(userId: string, data: any): Promise<any> {
    // Check user access to business
    const hasAccess = await this.checkUserBusinessAccess(userId, data.businessId)
    if (!hasAccess) {
      throw new Error('Access denied: User cannot create compliance for this business')
    }

    return await prisma.compliance.create({
      data: {
        ...data,
        status: 'PENDING'
      },
      include: {
        business: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })
  }

  async updateCompliance(complianceId: string, userId: string, data: any): Promise<any> {
    // First get the compliance to check business access
    const compliance = await this.getComplianceById(complianceId, userId)

    return await prisma.compliance.update({
      where: { id: complianceId },
      data,
      include: {
        business: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })
  }

  async getComplianceTemplates(countryId?: string): Promise<any> {
    const whereClause = countryId ? { countryId } : {}

    return await prisma.complianceTemplate.findMany({
      where: whereClause,
      orderBy: {
        name: 'asc'
      }
    })
  }

  async getOverdueCompliance(userId: string): Promise<any> {
    const accessibleBusinessIds = await this.getUserAccessibleBusinesses(userId)

    return await prisma.compliance.findMany({
      where: {
        businessId: {
          in: accessibleBusinessIds
        },
        dueDate: {
          lt: new Date()
        },
        status: {
          in: ['PENDING', 'IN_PROGRESS']
        }
      },
      include: {
        business: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        dueDate: 'asc'
      }
    })
  }

  async getUpcomingCompliance(userId: string, days: number = 30): Promise<any> {
    const accessibleBusinessIds = await this.getUserAccessibleBusinesses(userId)
    const now = new Date()
    const futureDate = new Date(now.getTime() + days * 24 * 60 * 60 * 1000)

    return await prisma.compliance.findMany({
      where: {
        businessId: {
          in: accessibleBusinessIds
        },
        dueDate: {
          gte: now,
          lte: futureDate
        },
        status: {
          in: ['PENDING', 'IN_PROGRESS']
        }
      },
      include: {
        business: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        dueDate: 'asc'
      }
    })
  }

  private async getUserAccessibleBusinesses(userId: string): Promise<string[]> {
    const memberships = await prisma.userBusinessMembership.findMany({
      where: {
        userId,
        isActive: true
      },
      select: {
        businessId: true
      }
    })

    return memberships.map((m: any) => m.businessId)
  }

  private async checkUserBusinessAccess(userId: string, businessId: string): Promise<boolean> {
    const membership = await prisma.userBusinessMembership.findFirst({
      where: {
        userId,
        businessId,
        isActive: true
      }
    })

    return !!membership
  }
}
export const complianceService = new ComplianceService()



