{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "clean": "rm -rf node_modules &&  rm -rf .next", "typecheck": "tsc --noEmit"}, "dependencies": {"@clerk/backend": "^2.3.1", "@clerk/nextjs": "^6.23.0", "@expo/next-adapter": "^6.0.0", "@prisma/client": "^6.10.1", "app": "*", "next": "15.3.3", "next-intl": "4.3.1", "next-themes": "^0.4.6", "prisma": "^6.10.1", "react": "19.0.0", "react-dom": "19.0.0", "react-native-web": "~0.20.0", "ui": "*"}, "devDependencies": {"@types/node": "^22", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "@zeit/next-css": "^1.0.1", "eslint": "^9", "eslint-config-next": "15.3.3", "next-fonts": "^1.5.1", "next-images": "^1.8.5", "postcss": "^8", "prettier": "^3.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}