import { PrismaClient } from '@prisma/client'

// Test database setup utilities
export class TestDatabase {
  private static instance: TestDatabase
  private prisma: PrismaClient

  private constructor() {
    this.prisma = new PrismaClient({
      datasources: {
        db: {
          url: process.env.TEST_DATABASE_URL || process.env.DATABASE_URL
        }
      }
    })
  }

  static getInstance(): TestDatabase {
    if (!TestDatabase.instance) {
      TestDatabase.instance = new TestDatabase()
    }
    return TestDatabase.instance
  }

  async setup(): Promise<void> {
    // Connect to database
    await this.prisma.$connect()
    
    // Run migrations if needed
    // await this.prisma.$executeRaw`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`
  }

  async cleanup(): Promise<void> {
    // Clean up test data
    const tablenames = await this.prisma.$queryRaw<Array<{ tablename: string }>>`
      SELECT tablename FROM pg_tables WHERE schemaname='public'
    `

    const tables = tablenames
      .map(({ tablename }) => tablename)
      .filter(name => name !== '_prisma_migrations')
      .map(name => `"public"."${name}"`)
      .join(', ')

    try {
      await this.prisma.$executeRawUnsafe(`TRUNCATE TABLE ${tables} CASCADE;`)
    } catch (error) {
      console.log({ error })
    }
  }

  async teardown(): Promise<void> {
    await this.cleanup()
    await this.prisma.$disconnect()
  }

  getPrisma(): PrismaClient {
    return this.prisma
  }

  // Helper methods for creating test data
  async createTestUser(data: {
    clerkUserId?: string
    firstName?: string
    lastName?: string
    email?: string
  } = {}) {
    return this.prisma.user.create({
      data: {
        clerkUserId: data.clerkUserId || `clerk-${Date.now()}`,
        firstName: data.firstName || 'Test',
        lastName: data.lastName || 'User',
        language: 'en',
        timezone: 'UTC',
        emailAddresses: {
          create: {
            email: data.email || `test-${Date.now()}@example.com`,
            isPrimary: true,
            isVerified: true
          }
        }
      },
      include: {
        emailAddresses: true
      }
    })
  }

  async createTestBusiness(data: {
    name?: string
    countryId?: string
    ownerId?: string
  } = {}) {
    // First ensure we have a country
    const country = await this.prisma.country.upsert({
      where: { id: data.countryId || 'US' },
      update: {},
      create: {
        id: data.countryId || 'US',
        name: 'United States',
        code: 'US',
        currency: 'USD'
      }
    })

    const business = await this.prisma.business.create({
      data: {
        name: data.name || `Test Business ${Date.now()}`,
        countryId: country.id,
        registrationNumber: `REG-${Date.now()}`,
        status: 'ACTIVE',
        systemStatus: 'ACTIVE',
        registrationStatus: 'REGISTERED'
      }
    })

    // Create owner membership if ownerId provided
    if (data.ownerId) {
      await this.prisma.userBusinessMembership.create({
        data: {
          userId: data.ownerId,
          businessId: business.id,
          role: 'OWNER',
          isActive: true,
          lastAccessedAt: new Date()
        }
      })
    }

    return business
  }

  async createTestBusinessGroup(data: {
    name?: string
    description?: string
    businessIds?: string[]
  } = {}) {
    const group = await this.prisma.businessGroup.create({
      data: {
        name: data.name || `Test Group ${Date.now()}`,
        description: data.description || 'Test business group'
      }
    })

    // Add businesses to group if provided
    if (data.businessIds && data.businessIds.length > 0) {
      await this.prisma.businessWithGroupRelationship.createMany({
        data: data.businessIds.map(businessId => ({
          businessId,
          businessGroupId: group.id
        }))
      })
    }

    return group
  }

  async createTestCompliance(data: {
    title?: string
    businessId: string
    status?: string
    dueDate?: Date
  }) {
    return this.prisma.compliance.create({
      data: {
        title: data.title || `Test Compliance ${Date.now()}`,
        description: 'Test compliance requirement',
        businessId: data.businessId,
        status: data.status as any || 'PENDING',
        dueDate: data.dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        priority: 'MEDIUM'
      }
    })
  }
}

// Global test setup
export const setupTestDatabase = async () => {
  const testDb = TestDatabase.getInstance()
  await testDb.setup()
  return testDb
}

export const cleanupTestDatabase = async () => {
  const testDb = TestDatabase.getInstance()
  await testDb.cleanup()
}

export const teardownTestDatabase = async () => {
  const testDb = TestDatabase.getInstance()
  await testDb.teardown()
}
