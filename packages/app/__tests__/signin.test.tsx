import React from 'react'
import { render, fireEvent, waitFor } from '@testing-library/react-native'
import { SignIn } from '../screens/signin'

// Mock the UI components
jest.mock('@/ui/index', () => ({
  Text: ({ children, className, ...props }: any) => {
    const React = require('react')
    const { Text: RNText } = require('react-native')
    return React.createElement(RNText, { ...props, testID: 'text-component' }, children)
  },
  View: ({ children, className, ...props }: any) => {
    const React = require('react')
    const { View: RNView } = require('react-native')
    return React.createElement(RNView, { ...props, testID: 'view-component' }, children)
  }
}))

jest.mock('@/ui/link', () => ({
  Link: ({ children, href, ...props }: any) => {
    const React = require('react')
    const { TouchableOpacity } = require('react-native')
    return React.createElement(TouchableOpacity, { ...props, testID: 'link-component' }, children)
  }
}))

// Mock the auth API
const mockGenerateSignInToken = jest.fn()
jest.mock('@/lib/api', () => ({
  authApi: {
    GenerateSignInToken: mockGenerateSignInToken
  }
}))

// Mock console.error to avoid noise in tests
const originalConsoleError = console.error
beforeEach(() => {
  console.error = jest.fn()
  jest.clearAllMocks()
})

afterEach(() => {
  console.error = originalConsoleError
})

describe('SignIn Screen', () => {
  it('renders correctly', () => {
    const { getByText } = render(<SignIn />)
    
    expect(getByText('SignIn Screen')).toBeTruthy()
    expect(getByText('This is the signin screen. You can use this screen to sign in to your account.')).toBeTruthy()
  })

  it('displays the main title', () => {
    const { getByText } = render(<SignIn />)
    
    const titleElement = getByText('SignIn Screen')
    expect(titleElement).toBeTruthy()
  })

  it('displays the description text', () => {
    const { getByText } = render(<SignIn />)
    
    const descriptionElement = getByText('This is the signin screen. You can use this screen to sign in to your account.')
    expect(descriptionElement).toBeTruthy()
  })

  it('renders the generate token button', () => {
    const { getByText } = render(<SignIn />)
    
    const buttonElement = getByText('Generate Token')
    expect(buttonElement).toBeTruthy()
  })

  it('handles token generation successfully', async () => {
    const mockToken = 'test-token-123'
    mockGenerateSignInToken.mockResolvedValue({ data: mockToken })

    const { getByText } = render(<SignIn />)
    
    const button = getByText('Generate Token')
    fireEvent.press(button)

    await waitFor(() => {
      expect(mockGenerateSignInToken).toHaveBeenCalledWith({})
      expect(getByText(`Token: ${mockToken}`)).toBeTruthy()
    })
  })

  it('displays signup link when token is generated', async () => {
    const mockToken = 'test-token-123'
    mockGenerateSignInToken.mockResolvedValue({ data: mockToken })

    const { getByText } = render(<SignIn />)
    
    const button = getByText('Generate Token')
    fireEvent.press(button)

    await waitFor(() => {
      expect(getByText('Or Signup')).toBeTruthy()
    })
  })

  it('handles token generation error', async () => {
    const mockError = new Error('API Error')
    mockGenerateSignInToken.mockRejectedValue(mockError)

    const { getByText } = render(<SignIn />)
    
    const button = getByText('Generate Token')
    fireEvent.press(button)

    await waitFor(() => {
      expect(mockGenerateSignInToken).toHaveBeenCalledWith({})
      expect(console.error).toHaveBeenCalledWith(mockError)
    })
  })

  it('does not show token section initially', () => {
    const { queryByText } = render(<SignIn />)
    
    expect(queryByText(/Token:/)).toBeNull()
    expect(queryByText('Or Signup')).toBeNull()
  })

  describe('Component Structure', () => {
    it('renders the main container view', () => {
      const { getAllByTestId } = render(<SignIn />)
      
      const viewComponents = getAllByTestId('view-component')
      expect(viewComponents.length).toBeGreaterThan(0)
    })

    it('renders text components', () => {
      const { getAllByTestId } = render(<SignIn />)
      
      const textComponents = getAllByTestId('text-component')
      expect(textComponents.length).toBeGreaterThanOrEqual(2)
    })
  })

  describe('User Interaction', () => {
    it('allows multiple token generation attempts', async () => {
      const mockToken1 = 'token-1'
      const mockToken2 = 'token-2'
      
      mockGenerateSignInToken
        .mockResolvedValueOnce({ data: mockToken1 })
        .mockResolvedValueOnce({ data: mockToken2 })

      const { getByText } = render(<SignIn />)
      const button = getByText('Generate Token')

      // First generation
      fireEvent.press(button)
      await waitFor(() => {
        expect(getByText(`Token: ${mockToken1}`)).toBeTruthy()
      })

      // Second generation
      fireEvent.press(button)
      await waitFor(() => {
        expect(getByText(`Token: ${mockToken2}`)).toBeTruthy()
      })

      expect(mockGenerateSignInToken).toHaveBeenCalledTimes(2)
    })
  })

  describe('Content Verification', () => {
    it('contains signin-specific content', () => {
      const { getByText } = render(<SignIn />)
      
      expect(getByText(/signin/i)).toBeTruthy()
      expect(getByText(/sign in/i)).toBeTruthy()
      expect(getByText(/account/i)).toBeTruthy()
    })
  })

  describe('Navigation Elements', () => {
    it('shows signup link after token generation', async () => {
      mockGenerateSignInToken.mockResolvedValue({ data: 'test-token' })

      const { getByText, getAllByTestId } = render(<SignIn />)
      
      const button = getByText('Generate Token')
      fireEvent.press(button)

      await waitFor(() => {
        const linkComponents = getAllByTestId('link-component')
        expect(linkComponents.length).toBeGreaterThan(0)
        expect(getByText('Or Signup')).toBeTruthy()
      })
    })
  })

  describe('State Management', () => {
    it('manages token state correctly', async () => {
      const mockToken = 'state-test-token'
      mockGenerateSignInToken.mockResolvedValue({ data: mockToken })

      const { getByText, queryByText } = render(<SignIn />)
      
      // Initially no token
      expect(queryByText(/Token:/)).toBeNull()
      
      // Generate token
      const button = getByText('Generate Token')
      fireEvent.press(button)

      // Token should appear
      await waitFor(() => {
        expect(getByText(`Token: ${mockToken}`)).toBeTruthy()
      })
    })
  })
})
