name: Setup PR Info
description: Extract pull request and commit info
runs:
  using: composite
  steps:
    - run: |
        pr_number=${{ github.event.pull_request.number || '0' }}
        echo "PR_NUMBER=$pr_number" >> $GITHUB_ENV
        if [ "$pr_number" != "0" ]; then
          pr_title=$(gh pr view "$pr_number" --json title --jq '.title' || echo "PR Title Not Found")
          echo "PR_TITLE=$pr_title" >> $GITHUB_ENV
        fi
        last_committer_name=$(git log -1 --pretty=format:'%an')
        last_committer_github=$(gh api repos/${{ github.repository }}/commits/${{ github.sha }} --jq '.author.login')
        echo "LAST_COMMITTER_NAME=$last_committer_name" >> $GITHUB_ENV
        echo "LAST_COMMITTER_GITHUB=$last_committer_github" >> $GITHUB_ENV
        echo "PR Info: CIRCLE_PULL_REQUEST=${{ github.event.pull_request.html_url }}, PR_NUMBER=$pr_number, PR_TITLE=$pr_title"
        echo "Committer: LAST_COMMITTER_NAME=$last_committer_name, LAST_COMMITTER_GITHUB=$last_committer_github"
      shell: bash