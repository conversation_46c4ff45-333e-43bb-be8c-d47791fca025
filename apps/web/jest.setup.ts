import '@testing-library/jest-dom'

// Mock environment variables for web app
Object.defineProperty(process.env, 'NODE_ENV', { value: 'test' })
Object.defineProperty(process.env, 'DATABASE_URL', { value: 'postgresql://test:test@localhost:5432/test_db' })
Object.defineProperty(process.env, 'NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY', { value: 'test-clerk-key' })
Object.defineProperty(process.env, 'CLERK_SECRET_KEY', { value: 'test-clerk-secret' })

// Mock console methods for cleaner test output
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
}

// Mock fetch for API testing
global.fetch = jest.fn()

// Setup test timeout
jest.setTimeout(30000)

// Clear all mocks before each test
beforeEach(() => {
  jest.clearAllMocks()
})

// Clean up after each test
afterEach(() => {
  jest.restoreAllMocks()
})
