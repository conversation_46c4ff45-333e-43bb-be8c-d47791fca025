// Permission system utilities
// This will work with our Prisma-based RBAC system

import { prisma } from '@/apps/web/db'

export interface UserPermissions {
  businessId: string
  permissions: string[]
  role: string
}

export class PermissionService {
  // Check if user has specific permission for a business
  async hasPermission(
    userId: string,
    businessId: string,
    permission: string
  ): Promise<boolean> {
    const userPermissions = await this.getUserBusinessPermissions(userId, businessId)
    return userPermissions.includes(permission)
  }

  // Check if user has any of the specified permissions
  async hasAnyPermission(
    userId: string,
    businessId: string,
    permissions: string[]
  ): Promise<boolean> {
    const userPermissions = await this.getUserBusinessPermissions(userId, businessId)
    return permissions.some(permission => userPermissions.includes(permission))
  }

  // Check if user has all specified permissions
  async hasAllPermissions(
    userId: string,
    businessId: string,
    permissions: string[]
  ): Promise<boolean> {
    const userPermissions = await this.getUserBusinessPermissions(userId, businessId)
    return permissions.every(permission => userPermissions.includes(permission))
  }

  // Get all permissions for a user in a specific business
  async getUserBusinessPermissions(
    userId: string,
    businessId: string
  ): Promise<string[]> {
    // Get user's role assignments for this business
    const roleAssignments = await prisma.roleAssignment.findMany({
      where: {
        userId,
        isActive: true,
        revokedAt: null,
        scope: {
          type: 'BUSINESS',
          entityId: businessId,
        },
      },
      include: {
        role: {
          include: {
            rolePermissions: {
              include: {
                permission: true,
              },
            },
            // Include parent roles for inheritance
            parentRoles: {
              include: {
                parentRole: {
                  include: {
                    rolePermissions: {
                      include: {
                        permission: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    })

    const permissions = new Set<string>()

    // Collect permissions from direct roles
    for (const assignment of roleAssignments) {
      for (const rolePermission of assignment.role.rolePermissions) {
        permissions.add(rolePermission.permission.name)
      }

      // Collect permissions from parent roles (inheritance)
      for (const parentRole of assignment.role.parentRoles) {
        for (const rolePermission of parentRole.parentRole.rolePermissions) {
          permissions.add(rolePermission.permission.name)
        }
      }
    }

    return Array.from(permissions)
  }

  /* Get all businesses a user has access to with their roles */
  async getUserBusinessAccess(userId: string): Promise<UserPermissions[]> {
    const roleAssignments = await prisma.roleAssignment.findMany({
      where: {
        userId,
        isActive: true,
        revokedAt: null,
      },
      include: {
        role: {
          include: {
            rolePermissions: {
              include: {
                permission: true,
              },
            },
          },
        },
        scope: true,
      },
    })

    const businessAccess: UserPermissions[] = []

    for (const assignment of roleAssignments) {
      if (assignment.scope.type === 'BUSINESS') {
        const permissions = assignment.role.rolePermissions.map(
          (rp: any) => rp.permission.name
        )

        businessAccess.push({
          businessId: assignment.scope.entityId,
          permissions,
          role: assignment.role.title,
        })
      }
    }

    return businessAccess
  }

  /* Check if user is owner of a business */
  async isBusinessOwner(userId: string, businessId: string): Promise<boolean> {
    const ownerRole = await prisma.role.findFirst({
      where: {
        title: 'Owner',
        type: 'business',
      },
    })

    if (!ownerRole) return false

    const roleAssignment = await prisma.roleAssignment.findFirst({
      where: {
        userId,
        roleId: ownerRole.id,
        isActive: true,
        revokedAt: null,
        scope: {
          type: 'BUSINESS',
          entityId: businessId,
        },
      },
    })

    return !!roleAssignment
  }

  // Check if user has admin role in a business
  async isBusinessAdmin(userId: string, businessId: string): Promise<boolean> {
    const adminRole = await prisma.role.findFirst({
      where: {
        title: 'Admin',
        type: 'business',
      },
    })

    if (!adminRole) return false

    const roleAssignment = await prisma.roleAssignment.findFirst({
      where: {
        userId,
        roleId: adminRole.id,
        isActive: true,
        revokedAt: null,
        scope: {
          type: 'BUSINESS',
          entityId: businessId,
        },
      },
    })

    return !!roleAssignment
  }
}

export const permissionService = new PermissionService()

// Common permission constants
export const PERMISSIONS = {
  // Business permissions
  BUSINESS_VIEW: 'canViewBusiness',
  BUSINESS_EDIT: 'canEditBusiness',
  BUSINESS_DELETE: 'canDeleteBusiness',
  BUSINESS_TRANSFER: 'canTransferOwnership',

  // Compliance permissions
  COMPLIANCE_VIEW: 'canViewCompliance',
  COMPLIANCE_CREATE: 'canCreateCompliance',
  COMPLIANCE_EDIT: 'canEditCompliance',
  COMPLIANCE_DELETE: 'canDeleteCompliance',
  COMPLIANCE_SUBMIT: 'canSubmitCompliance',

  // Document permissions
  DOCUMENT_VIEW: 'canViewDocuments',
  DOCUMENT_UPLOAD: 'canUploadDocuments',
  DOCUMENT_EDIT: 'canEditDocuments',
  DOCUMENT_DELETE: 'canDeleteDocuments',
  DOCUMENT_SHARE: 'canShareDocuments',

  // User management permissions
  USER_INVITE: 'canInviteUsers',
  USER_MANAGE: 'canManageUsers',
  USER_REMOVE: 'canRemoveUsers',

  // Role permissions
  ROLE_VIEW: 'canViewRoles',
  ROLE_ASSIGN: 'canAssignRoles',
  ROLE_CREATE: 'canCreateRoles',
  ROLE_EDIT: 'canEditRoles',

  // Integration permissions
  INTEGRATION_VIEW: 'canViewIntegrations',
  INTEGRATION_MANAGE: 'canManageIntegrations',

  // Settings permissions
  SETTINGS_VIEW: 'canViewSettings',
  SETTINGS_EDIT: 'canEditSettings',
} as const
