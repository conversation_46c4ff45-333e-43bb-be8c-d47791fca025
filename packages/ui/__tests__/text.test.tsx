/**
 * Text Component Tests
 *
 * Simple tests to verify the Text component works correctly
 */

import { Text } from '../text'

// Mock nativewind
jest.mock('nativewind', () => ({
  cssInterop: jest.fn(),
}))

// Mock the cn utility
jest.mock('@/packages/ui/utils/cn', () => ({
  cn: (...classes: any[]) => classes.filter(Boolean).join(' ')
}))

describe('Text Component - Basic Tests', () => {
  it('exists and can be imported', () => {
    expect(Text).toBeDefined()
    expect(typeof Text).toBe('function')
  })

  it('has correct displayName', () => {
    expect(Text.displayName).toBe('Text')
  })

  it('is a React component', () => {
    // Test that it's a valid React component
    expect(Text).toHaveProperty('$$typeof')
  })

  it('accepts props correctly', () => {
    // Test that the component accepts the expected props
    const props = {
      children: 'Hello World',
      className: 'text-class'
    }

    // Should not throw when creating element
    expect(() => {
      const element = Text(props)
      expect(element).toBeDefined()
    }).not.toThrow()
  })

  describe('Component Structure', () => {
    it('handles string children', () => {
      const props = { children: 'Simple string' }
      expect(() => Text(props)).not.toThrow()
    })

    it('handles number children', () => {
      const props = { children: 42 }
      expect(() => Text(props)).not.toThrow()
    })

    it('handles React node children', () => {
      const props = { children: 'React node content' }
      expect(() => Text(props)).not.toThrow()
    })

    it('handles className prop', () => {
      const props = { children: 'Styled text', className: 'text-lg font-bold' }
      expect(() => Text(props)).not.toThrow()
    })

    it('handles undefined className', () => {
      const props = { children: 'No class text', className: undefined }
      expect(() => Text(props)).not.toThrow()
    })
  })

  describe('React Native Text Props', () => {
    it('accepts numberOfLines prop', () => {
      const props = { children: 'Truncated text', numberOfLines: 2 }
      expect(() => Text(props)).not.toThrow()
    })

    it('accepts ellipsizeMode prop', () => {
      const props = { children: 'Ellipsized text', ellipsizeMode: 'tail' as const }
      expect(() => Text(props)).not.toThrow()
    })

    it('accepts style prop', () => {
      const props = {
        children: 'Styled text',
        style: { fontSize: 16, color: 'red' }
      }
      expect(() => Text(props)).not.toThrow()
    })

    it('accepts accessibility props', () => {
      const props = {
        children: 'Accessible text',
        accessibilityLabel: 'Custom label',
        accessibilityHint: 'Custom hint'
      }
      expect(() => Text(props)).not.toThrow()
    })

    it('accepts selection props', () => {
      const props = {
        children: 'Selectable text',
        selectable: true,
        selectionColor: 'blue'
      }
      expect(() => Text(props)).not.toThrow()
    })
  })

  describe('Ref Forwarding', () => {
    it('is a forwardRef component', () => {
      // Check if it's wrapped with forwardRef
      expect(Text.$$typeof).toBeDefined()
    })
  })
})
