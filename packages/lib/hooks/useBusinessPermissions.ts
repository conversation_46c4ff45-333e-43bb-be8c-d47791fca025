// import { useBusinessUser } from '@/packages/lib/contexts/business-user-context'
// import { BUSINESS_PERMISSIONS, BUSINESS_ROLES } from '@/apps/web/library/types/business-user-context'

// /**
//  * Hook for checking business-scoped permissions
//  * Only works within BusinessUserProvider context
//  */
// export function useBusinessPermissions() {
//   const { hasPermission, hasRole, canAccess, isOwner, isAdmin } = useBusinessUser()

//   return {
//     // Core permission checks
//     hasPermission,
//     hasRole,
//     canAccess,
    
//     // Role checks
//     isOwner,
//     isAdmin,
    
//     // User management permissions
//     canViewUsers: hasPermission(BUSINESS_PERMISSIONS.USERS_VIEW) || isAdmin,
//     canManageUsers: hasPermission(BUSINESS_PERMISSIONS.USERS_MANAGE) || isOwner,
//     canInviteUsers: hasPermission(BUSINESS_PERMISSIONS.USERS_INVITE) || isAdmin,
//     canRemoveUsers: hasPermission(BUSINESS_PERMISSIONS.USERS_REMOVE) || isOwner,
    
//     // Business settings permissions
//     canViewSettings: hasPermission(BUSINESS_PERMISSIONS.SETTINGS_VIEW) || isAdmin,
//     canManageSettings: hasPermission(BUSINESS_PERMISSIONS.SETTINGS_MANAGE) || isAdmin,
//     canManageBusiness: hasPermission(BUSINESS_PERMISSIONS.BUSINESS_MANAGE) || isOwner,
    
//     // Financial permissions
//     canViewFinance: hasPermission(BUSINESS_PERMISSIONS.FINANCE_VIEW) || isAdmin,
//     canManageFinance: hasPermission(BUSINESS_PERMISSIONS.FINANCE_MANAGE) || isAdmin,
//     canCreateInvoices: hasPermission(BUSINESS_PERMISSIONS.INVOICES_CREATE) || isAdmin,
//     canManagePayments: hasPermission(BUSINESS_PERMISSIONS.PAYMENTS_MANAGE) || isAdmin,
    
//     // Orders & Inventory permissions
//     canViewOrders: hasPermission(BUSINESS_PERMISSIONS.ORDERS_VIEW),
//     canManageOrders: hasPermission(BUSINESS_PERMISSIONS.ORDERS_MANAGE) || isAdmin,
//     canViewInventory: hasPermission(BUSINESS_PERMISSIONS.INVENTORY_VIEW),
//     canManageInventory: hasPermission(BUSINESS_PERMISSIONS.INVENTORY_MANAGE) || isAdmin,
    
//     // CRM permissions
//     canViewCRM: hasPermission(BUSINESS_PERMISSIONS.CRM_VIEW),
//     canManageCRM: hasPermission(BUSINESS_PERMISSIONS.CRM_MANAGE) || isAdmin,
//     canManageCustomers: hasPermission(BUSINESS_PERMISSIONS.CUSTOMERS_MANAGE) || isAdmin,
    
//     // Documents & Compliance permissions
//     canViewDocuments: hasPermission(BUSINESS_PERMISSIONS.DOCUMENTS_VIEW),
//     canManageDocuments: hasPermission(BUSINESS_PERMISSIONS.DOCUMENTS_MANAGE) || isAdmin,
//     canViewCompliance: hasPermission(BUSINESS_PERMISSIONS.COMPLIANCE_VIEW),
//     canManageCompliance: hasPermission(BUSINESS_PERMISSIONS.COMPLIANCE_MANAGE) || isAdmin,
    
//     // Analytics & Reports permissions
//     canViewAnalytics: hasPermission(BUSINESS_PERMISSIONS.ANALYTICS_VIEW) || isAdmin,
//     canViewReports: hasPermission(BUSINESS_PERMISSIONS.REPORTS_VIEW) || isAdmin,
//     canExportReports: hasPermission(BUSINESS_PERMISSIONS.REPORTS_EXPORT) || isAdmin,
//   }
// }

// /**
//  * Hook for checking specific business role
//  */
// export function useBusinessRole(role: keyof typeof BUSINESS_ROLES) {
//   const { hasRole } = useBusinessUser()
//   return hasRole(BUSINESS_ROLES[role])
// }

// /**
//  * Hook for checking if user is business owner
//  */
// export function useIsBusinessOwner() {
//   const { isOwner } = useBusinessUser()
//   return isOwner
// }

// /**
//  * Hook for checking if user is business admin (owner or admin)
//  */
// export function useIsBusinessAdmin() {
//   const { isAdmin } = useBusinessUser()
//   return isAdmin
// }

// /**
//  * Hook for getting user's business context info
//  */
// export function useBusinessContext() {
//   const { 
//     businessId, 
//     businessName, 
//     userRole, 
//     permissions, 
//     roles,
//     businessUser 
//   } = useBusinessUser()
  
//   return {
//     businessId,
//     businessName,
//     userRole,
//     permissions,
//     roles,
//     businessUser,
    
//     // Computed properties
//     hasAnyPermissions: permissions.length > 0,
//     hasMultipleRoles: roles.length > 1,
//     isActiveUser: businessUser?.isActive || false,
//     lastAccessed: businessUser?.lastAccessedAt,
//   }
// }

// /**
//  * Hook for business feature access checks
//  */
// export function useBusinessFeatureAccess() {
//   const permissions = useBusinessPermissions()
  
//   return {
//     // Feature modules access
//     canAccessStore: permissions.canViewOrders || permissions.canManageOrders,
//     canAccessOrders: permissions.canViewOrders,
//     canAccessInventory: permissions.canViewInventory,
//     canAccessCRM: permissions.canViewCRM,
//     canAccessFinance: permissions.canViewFinance,
//     canAccessCompliance: permissions.canViewCompliance,
//     canAccessDocuments: permissions.canViewDocuments,
//     canAccessAnalytics: permissions.canViewAnalytics,
//     canAccessSettings: permissions.canViewSettings,
//     canAccessUserManagement: permissions.canViewUsers,
    
//     // Admin features
//     canAccessAdminPanel: permissions.isAdmin,
//     canAccessSecureVault: permissions.isAdmin,
//     canAccessIntegrations: permissions.isAdmin,
//     canAccessBusinessGroups: permissions.isOwner,
    
//     // Management capabilities
//     canManageAnyFeature: (
//       permissions.canManageOrders ||
//       permissions.canManageInventory ||
//       permissions.canManageCRM ||
//       permissions.canManageFinance ||
//       permissions.canManageCompliance ||
//       permissions.canManageDocuments ||
//       permissions.canManageUsers ||
//       permissions.canManageSettings
//     ),
//   }
// }
