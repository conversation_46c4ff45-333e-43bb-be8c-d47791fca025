# useBusinessUser Hook

## 📖 Overview

The `useBusinessUser` hook provides business-scoped user context and permissions. It can only be used within a `BusinessUserProvider` and automatically scopes all permission checks to the current business context.

## 🎯 Purpose

- **Business-Scoped Context**: All data automatically scoped to current business
- **Zero Redundancy**: Single API call shared across all child components
- **Permission Optimization**: Pre-computed business-specific permissions
- **Layout Integration**: Designed for business workspace layouts

## ⚠️ Requirements

**Must be used within `BusinessUserProvider`:**

```typescript
<BusinessUserProvider businessId="business-123">
  <ComponentUsingBusinessUser /> {/* ✅ Works */}
</BusinessUserProvider>

<ComponentUsingBusinessUser /> {/* ❌ Throws error */}
```

## 📝 Signature

```typescript
function useBusinessUser(): BusinessUserContextData
```

### Return Value

```typescript
interface BusinessUserContextData {
  // Core user data scoped to current business
  user: VentureUser | null
  businessUser: VentureUserBusinessContext | null
  
  // Loading and error states
  isLoading: boolean
  error: string | null
  
  // Business context
  businessId: string
  businessName: string | null
  userRole: string | null
  
  // Permission helpers (automatically scoped to current business)
  hasPermission: (permission: string) => boolean
  hasRole: (role: string) => boolean
  canAccess: (resource: string, action: string) => boolean
  
  // Role checks (scoped to current business)
  isOwner: boolean
  isAdmin: boolean
  isMember: boolean
  canManageUsers: boolean
  canManageSettings: boolean
  canViewFinance: boolean
  canManageOrders: boolean
  
  // Business-specific data
  permissions: string[]
  roles: string[]
  
  // Actions
  refetch: () => Promise<void>
  refresh: () => Promise<void>
}
```

## 🚀 Basic Usage

### Business Context Information

```typescript
import { useBusinessUser } from '@/lib/contexts/business-user-context'

function BusinessHeader() {
  const { 
    businessName, 
    userRole, 
    isOwner, 
    isAdmin 
  } = useBusinessUser()
  
  return (
    <header>
      <h1>{businessName}</h1>
      <div className="user-info">
        <span>Role: {userRole}</span>
        {isOwner && <Badge variant="warning">Owner</Badge>}
        {isAdmin && <Badge variant="info">Admin</Badge>}
      </div>
    </header>
  )
}
```

### Permission-Based Rendering

```typescript
function BusinessDashboard() {
  const { 
    hasPermission,
    canManageUsers,
    canViewFinance,
    isAdmin 
  } = useBusinessUser()
  
  return (
    <div>
      <h1>Business Dashboard</h1>
      
      {/* Permission-based features */}
      {hasPermission('orders.view') && (
        <OrdersWidget />
      )}
      
      {canViewFinance && (
        <FinancialWidget />
      )}
      
      {canManageUsers && (
        <UserManagementWidget />
      )}
      
      {/* Role-based features */}
      {isAdmin && (
        <AdminPanel />
      )}
    </div>
  )
}
```

### Navigation with Permissions

```typescript
function BusinessSidebar() {
  const { hasPermission, isAdmin, isOwner } = useBusinessUser()
  
  const navigationItems = [
    { name: 'Dashboard', href: '/dashboard', show: true },
    { name: 'Orders', href: '/orders', show: hasPermission('orders.view') },
    { name: 'Finance', href: '/finance', show: hasPermission('finance.view') },
    { name: 'Users', href: '/users', show: hasPermission('users.view') },
    { name: 'Settings', href: '/settings', show: isAdmin },
    { name: 'Business Groups', href: '/groups', show: isOwner },
  ]
  
  return (
    <nav>
      {navigationItems
        .filter(item => item.show)
        .map(item => (
          <NavItem key={item.name} href={item.href}>
            {item.name}
          </NavItem>
        ))
      }
    </nav>
  )
}
```

## 🔒 Permission Patterns

### Resource-Based Access Control

```typescript
function OrderManagement() {
  const { 
    hasPermission,
    canAccess,
    isAdmin 
  } = useBusinessUser()
  
  // Check specific permissions
  const canViewOrders = hasPermission('orders.view')
  const canCreateOrders = hasPermission('orders.create')
  const canDeleteOrders = hasPermission('orders.delete')
  
  // Check resource + action combinations
  const canExportData = canAccess('orders', 'export')
  const canBulkUpdate = canAccess('orders', 'bulk_update')
  
  if (!canViewOrders) {
    return <AccessDenied feature="Orders" />
  }
  
  return (
    <div>
      <OrderList />
      
      {canCreateOrders && (
        <Button>Create Order</Button>
      )}
      
      {canDeleteOrders && (
        <Button variant="destructive">Delete Selected</Button>
      )}
      
      {canExportData && (
        <Button>Export Orders</Button>
      )}
      
      {isAdmin && (
        <Button>Order Settings</Button>
      )}
    </div>
  )
}
```

### Role-Based Features

```typescript
function UserManagement() {
  const { 
    isOwner,
    isAdmin,
    canManageUsers,
    hasRole 
  } = useBusinessUser()
  
  return (
    <div>
      <h1>User Management</h1>
      
      {/* Basic user management for admins */}
      {canManageUsers && (
        <UserList />
      )}
      
      {/* Role assignment for admins */}
      {isAdmin && (
        <RoleAssignment />
      )}
      
      {/* Ownership transfer for owners only */}
      {isOwner && (
        <OwnershipTransfer />
      )}
      
      {/* Custom role checks */}
      {hasRole('hr_manager') && (
        <HRFeatures />
      )}
    </div>
  )
}
```

## 🎨 Layout Integration

### Business Workspace Layout

```typescript
import { BusinessUserProvider } from '@/lib/contexts/business-user-context'

function BusinessWorkspace({ businessId, children }) {
  return (
    <BusinessUserProvider businessId={businessId}>
      <div className="business-layout">
        <BusinessHeader />
        <div className="flex">
          <BusinessSidebar />
          <main className="flex-1">
            {children}
          </main>
        </div>
      </div>
    </BusinessUserProvider>
  )
}

// Usage in pages
function OrdersPage() {
  // useBusinessUser works here because we're inside BusinessUserProvider
  const { canManageOrders } = useBusinessUser()
  
  return (
    <div>
      <h1>Orders</h1>
      {canManageOrders && <OrderActions />}
    </div>
  )
}
```

### Conditional Layout Elements

```typescript
function BusinessHeader() {
  const { 
    businessName,
    userRole,
    canManageUsers,
    isOwner,
    permissions 
  } = useBusinessUser()
  
  return (
    <header className="business-header">
      <div className="business-info">
        <h1>{businessName}</h1>
        <span className="role">{userRole}</span>
      </div>
      
      <div className="actions">
        {canManageUsers && (
          <Button>Manage Team</Button>
        )}
        
        {isOwner && (
          <Button>Business Settings</Button>
        )}
        
        <UserMenu />
      </div>
      
      {/* Debug info in development */}
      {process.env.NODE_ENV === 'development' && (
        <div className="debug-info">
          <small>Permissions: {permissions.length}</small>
        </div>
      )}
    </header>
  )
}
```

## 🔄 Advanced Usage

### Error Handling

```typescript
function RobustBusinessComponent() {
  const { 
    user,
    businessName,
    isLoading,
    error,
    refresh 
  } = useBusinessUser()
  
  if (isLoading) {
    return (
      <div className="loading-state">
        <Spinner />
        <p>Loading business context...</p>
      </div>
    )
  }
  
  if (error) {
    return (
      <div className="error-state">
        <AlertCircle className="error-icon" />
        <h3>Failed to load business context</h3>
        <p>{error}</p>
        <Button onClick={refresh}>Try Again</Button>
      </div>
    )
  }
  
  if (!user) {
    return (
      <div className="no-access">
        <Lock className="lock-icon" />
        <h3>Access Denied</h3>
        <p>You don't have access to this business.</p>
      </div>
    )
  }
  
  return (
    <div>
      <h1>Welcome to {businessName}</h1>
      <BusinessContent />
    </div>
  )
}
```

### Dynamic Permission Checks

```typescript
function DynamicFeatureAccess() {
  const { hasPermission, permissions } = useBusinessUser()
  
  // Dynamic feature list based on permissions
  const availableFeatures = [
    { 
      name: 'Orders', 
      permission: 'orders.view',
      component: OrdersFeature 
    },
    { 
      name: 'Finance', 
      permission: 'finance.view',
      component: FinanceFeature 
    },
    { 
      name: 'Analytics', 
      permission: 'analytics.view',
      component: AnalyticsFeature 
    },
  ].filter(feature => hasPermission(feature.permission))
  
  return (
    <div>
      <h1>Available Features</h1>
      <p>You have access to {availableFeatures.length} features</p>
      
      {availableFeatures.map(feature => (
        <div key={feature.name}>
          <h3>{feature.name}</h3>
          <feature.component />
        </div>
      ))}
      
      {/* Debug: Show all permissions */}
      <details>
        <summary>All Permissions ({permissions.length})</summary>
        <ul>
          {permissions.map(permission => (
            <li key={permission}>{permission}</li>
          ))}
        </ul>
      </details>
    </div>
  )
}
```

## 🎣 Related Hooks

### useIsInBusinessContext

Check if component is within business context:

```typescript
import { useIsInBusinessContext } from '@/lib/contexts/business-user-context'

function ConditionalBusinessFeature() {
  const isInBusinessContext = useIsInBusinessContext()
  
  if (!isInBusinessContext) {
    return <GlobalFeature />
  }
  
  // Safe to use useBusinessUser here
  const { businessName } = useBusinessUser()
  return <BusinessSpecificFeature businessName={businessName} />
}
```

## 🚨 Common Pitfalls

### 1. **Using Outside Provider**

```typescript
// ❌ Will throw error
function MyComponent() {
  const { businessName } = useBusinessUser() // Error: not in provider
}

// ✅ Correct usage
<BusinessUserProvider businessId="123">
  <MyComponent /> {/* Now it works */}
</BusinessUserProvider>
```

### 2. **Business ID Changes**

```typescript
// ❌ Provider doesn't update when businessId changes
<BusinessUserProvider businessId={oldBusinessId}>
  <BusinessContent />
</BusinessUserProvider>

// ✅ Provider automatically handles businessId changes
<BusinessUserProvider businessId={currentBusinessId}>
  <BusinessContent />
</BusinessUserProvider>
```

### 3. **Permission Scope Confusion**

```typescript
// ❌ Trying to check permissions for different business
const { hasPermission } = useBusinessUser()
const canManageOtherBusiness = hasPermission('users.manage') // Only current business

// ✅ Use global hook for cross-business checks
const { hasPermission: globalHasPermission } = useVentureUser()
const canManageOtherBusiness = globalHasPermission('users.manage', otherBusinessId)
```

## 🎯 Best Practices

1. **Always use within BusinessUserProvider**
2. **Leverage automatic business scoping**
3. **Handle loading and error states**
4. **Use for business workspace layouts**
5. **Prefer over global hooks in business context**
6. **Test permission boundaries thoroughly**
7. **Document custom permission requirements**
