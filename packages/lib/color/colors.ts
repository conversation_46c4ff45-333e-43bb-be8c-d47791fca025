// VentureDirection Design System - Blue & White Color Palette

export const ventureColors = {
  // Primary Blue Palette
  primary: {
    50: '#eff6ff',   // Very light blue - backgrounds, hover states
    100: '#dbeafe',  // Light blue - subtle backgrounds
    200: '#bfdbfe',  // Lighter blue - borders, dividers
    300: '#93c5fd',  // Medium light blue - disabled states
    400: '#60a5fa',  // Medium blue - secondary actions
    500: '#3b82f6',  // Main brand blue - primary actions, logos
    600: '#2563eb',  // Dark blue - text, emphasis
    700: '#1d4ed8',  // Darker blue - hover states
    800: '#1e40af',  // Very dark blue - active states
    900: '#1e3a8a',  // Darkest blue - high contrast text
  },

  // Neutral Grays (for text and backgrounds)
  neutral: {
    50: '#f8fafc',   // Almost white - page backgrounds
    100: '#f1f5f9',  // Very light gray - card backgrounds
    200: '#e2e8f0',  // Light gray - borders
    300: '#cbd5e1',  // Medium light gray - disabled text
    400: '#94a3b8',  // Medium gray - placeholder text
    500: '#64748b',  // Gray - secondary text
    600: '#475569',  // Dark gray - body text
    700: '#334155',  // Darker gray - headings
    800: '#1e293b',  // Very dark gray - emphasis
    900: '#0f172a',  // Almost black - high contrast
  },

  // Pure Colors
  white: '#ffffff',
  black: '#000000',

  // Semantic Colors (using blue tints)
  success: {
    50: '#f0fdf4',
    500: '#22c55e',
    600: '#16a34a',
  },
  warning: {
    50: '#fffbeb',
    500: '#f59e0b',
    600: '#d97706',
  },
  error: {
    50: '#fef2f2',
    500: '#ef4444',
    600: '#dc2626',
  },
  info: {
    50: '#eff6ff',   // Same as primary-50
    500: '#3b82f6',  // Same as primary-500
    600: '#2563eb',  // Same as primary-600
  },
} as const

// Component-specific color mappings
export const componentColors = {
  // Buttons
  button: {
    primary: {
      bg: ventureColors.primary[500],
      bgHover: ventureColors.primary[600],
      bgActive: ventureColors.primary[700],
      text: ventureColors.white,
      border: ventureColors.primary[500],
    },
    secondary: {
      bg: ventureColors.white,
      bgHover: ventureColors.primary[50],
      bgActive: ventureColors.primary[100],
      text: ventureColors.primary[600],
      border: ventureColors.primary[200],
    },
    ghost: {
      bg: 'transparent',
      bgHover: ventureColors.primary[50],
      bgActive: ventureColors.primary[100],
      text: ventureColors.primary[600],
      border: 'transparent',
    },
  },

  // Navigation
  navigation: {
    bg: ventureColors.white,
    border: ventureColors.neutral[200],
    text: ventureColors.neutral[600],
    textActive: ventureColors.primary[600],
    bgActive: ventureColors.primary[50],
    bgHover: ventureColors.neutral[50],
  },

  // Cards
  card: {
    bg: ventureColors.white,
    border: ventureColors.neutral[200],
    shadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)',
  },

  // Forms
  form: {
    bg: ventureColors.white,
    border: ventureColors.neutral[300],
    borderFocus: ventureColors.primary[500],
    text: ventureColors.neutral[900],
    placeholder: ventureColors.neutral[400],
    label: ventureColors.neutral[700],
  },

  // Status indicators
  status: {
    active: ventureColors.success[500],
    inactive: ventureColors.neutral[400],
    pending: ventureColors.warning[500],
    error: ventureColors.error[500],
  },

  // Business context
  business: {
    header: {
      bg: ventureColors.white,
      border: ventureColors.neutral[200],
      text: ventureColors.neutral[900],
      accent: ventureColors.primary[500],
    },
    sidebar: {
      bg: ventureColors.white,
      border: ventureColors.neutral[200],
      text: ventureColors.neutral[600],
      textActive: ventureColors.primary[600],
      bgActive: ventureColors.primary[50],
      bgHover: ventureColors.neutral[50],
    },
  },
} as const

// Utility functions
export const getColorValue = (colorPath: string): string => {
  const keys = colorPath.split('.')
  let value: any = ventureColors
  
  for (const key of keys) {
    value = value[key]
    if (value === undefined) {
      console.warn(`Color path "${colorPath}" not found`)
      return ventureColors.primary[500] // fallback
    }
  }
  
  return value
}

export const rgba = (hex: string, alpha: number): string => {
  const r = parseInt(hex.slice(1, 3), 16)
  const g = parseInt(hex.slice(3, 5), 16)
  const b = parseInt(hex.slice(5, 7), 16)
  return `rgba(${r}, ${g}, ${b}, ${alpha})`
}
