pre-commit:
  parallel: true
  cache: true 
  commands:
    lint:
      glob: "*.{ts,tsx}"
      run: |
        echo "🔍 Running ESLint on staged files..." 
        yarn lint {staged_files}
    typecheck:
      glob: "*.{ts,tsx}"
      run: |
        echo "🧠 Running TypeScript type check..." 
        yarn typecheck
    test:
      run: |
        echo "🧪 Running tests with coverage..."
        yarn test:runner api && yarn test:runner services
