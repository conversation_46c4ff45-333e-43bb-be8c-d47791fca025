# VentureDirection Colour Standardization Checklist

## ✅ Completed Standardizations

### Core Design System
- [x] **Colour Palette Defined** - Blue and white theme with venture-* classes
- [x] **Tailwind Config Updated** - VentureDirection colours added to theme
- [x] **CSS Variables Updated** - HSL values for consistent theming
- [x] **Design System Documentation** - Complete colour usage guide

### Components Standardized
- [x] **Button Component** - Updated with venture-* colours and new variants
- [x] **Badge Component** - Standardized with venture-* colour scheme
- [x] **Business Header** - Updated logo and accent colours
- [x] **Business Sidebar** - Standardized navigation colours
- [x] **VentureLogo Component** - Created with brand colours

### Utility Components
- [x] **Colour Palette Component** - Visual reference for all colours
- [x] **Design System Colours** - TypeScript colour definitions

## 🔄 Components to Standardize

### Navigation Components
- [ ] **Dashboard Layout** - Update navigation colours
- [ ] **Language Switcher** - Standardize with venture colours
- [ ] **User Menu** - Update dropdown colours
- [ ] **Breadcrumbs** - Apply venture colour scheme

### Form Components
- [ ] **Input Fields** - Update focus states with venture-500
- [ ] **Select Dropdowns** - Standardize selection colours
- [ ] **Checkboxes/Radio** - Update with venture accent colours
- [ ] **Form Labels** - Consistent text colours

### Business Components
- [ ] **Business Switcher** - Update with venture colours
- [ ] **Onboarding Components** - Standardize progress indicators
- [ ] **Permission Guards** - Update access denied states
- [ ] **User Profile** - Standardize profile display

### Dashboard Components
- [ ] **Dashboard Cards** - Update card styling
- [ ] **Statistics Widgets** - Standardize metric displays
- [ ] **Charts/Graphs** - Update with venture colour palette
- [ ] **Data Tables** - Standardize table styling

### Status Indicators
- [ ] **Loading Spinners** - Update with venture-500
- [ ] **Progress Bars** - Standardize progress colours
- [ ] **Status Badges** - Ensure consistent status colours
- [ ] **Notification Toasts** - Update notification styling

## 🎨 Colour Usage Guidelines

### Primary Actions
```css
/* Use venture-500 for primary buttons and actions */
bg-venture-500 hover:bg-venture-600 active:bg-venture-700

/* Use venture-600 for primary text and links */
text-venture-600 hover:text-venture-700
```

### Secondary Actions
```css
/* Use outline style with venture colours */
border-venture-200 text-venture-600 hover:bg-venture-50

/* Use ghost style for subtle actions */
text-venture-600 hover:bg-venture-50
```

### Status Colours
```css
/* Success states */
bg-success-500 text-white
bg-success-100 text-success-800

/* Warning states */
bg-warning-500 text-white
bg-warning-100 text-warning-800

/* Error states */
bg-error-500 text-white
bg-error-100 text-error-800
```

### Backgrounds
```css
/* Page backgrounds */
bg-white

/* Card backgrounds */
bg-white border-gray-200

/* Hover states */
hover:bg-venture-50

/* Active states */
bg-venture-100
```

## 🔍 Components Needing Review

### High Priority
1. **Dashboard Layout** - Main navigation and layout
2. **Business Header** - Primary business interface
3. **Form Components** - User input consistency
4. **Status Indicators** - Visual feedback consistency

### Medium Priority
1. **Data Tables** - Business data display
2. **Charts/Graphs** - Analytics visualization
3. **Modal Dialogs** - Overlay consistency
4. **Dropdown Menus** - Selection interfaces

### Low Priority
1. **Footer Components** - Secondary navigation
2. **Help/Documentation** - Support interfaces
3. **Error Pages** - Error state styling
4. **Loading States** - Async operation feedback

## 🛠️ Implementation Steps

### For Each Component:

1. **Audit Current Colours**
   ```bash
   # Search for colour classes in component
   grep -r "bg-blue\|text-blue\|border-blue" components/
   ```

2. **Update Colour Classes**
   ```tsx
   // Replace generic blue classes
   - className="bg-blue-500"
   + className="bg-venture-500"
   
   // Replace primary classes
   - className="bg-primary"
   + className="bg-venture-500"
   ```

3. **Test Visual Consistency**
   - Check hover states
   - Verify focus states
   - Test active states
   - Ensure accessibility

4. **Update Documentation**
   - Add component to design system docs
   - Include usage examples
   - Document colour variants

## 📋 Standardization Script

### Automated Colour Replacement
```bash
#!/bin/bash
# Replace common colour patterns

# Replace blue-500 with venture-500
find components/ -name "*.tsx" -exec sed -i 's/bg-blue-500/bg-venture-500/g' {} \;
find components/ -name "*.tsx" -exec sed -i 's/text-blue-600/text-venture-600/g' {} \;
find components/ -name "*.tsx" -exec sed -i 's/border-blue-200/border-venture-200/g' {} \;

# Replace primary with venture
find components/ -name "*.tsx" -exec sed -i 's/bg-primary/bg-venture-500/g' {} \;
find components/ -name "*.tsx" -exec sed -i 's/text-primary/text-venture-600/g' {} \;

echo "Colour standardization complete!"
```

## ✅ Quality Checklist

### Before Marking Complete:
- [ ] Component uses venture-* colour classes
- [ ] Hover states are consistent
- [ ] Focus states use venture-500 ring
- [ ] Active states are properly styled
- [ ] Accessibility contrast ratios met
- [ ] Component documented in design system
- [ ] Visual regression testing passed

### Accessibility Requirements:
- [ ] Text contrast ratio ≥ 4.5:1
- [ ] Focus indicators clearly visible
- [ ] Colour not the only way to convey information
- [ ] High contrast mode compatible

## 🎯 Success Metrics

### Visual Consistency
- All components use venture-* colour palette
- Consistent hover/focus/active states
- Unified brand appearance

### Developer Experience
- Clear colour usage guidelines
- Standardized component APIs
- Comprehensive documentation

### User Experience
- Professional, cohesive interface
- Accessible colour combinations
- Intuitive visual hierarchy

## 📅 Timeline

### Week 1: Core Components
- Navigation and layout components
- Primary action components (buttons, links)
- Form components

### Week 2: Business Components
- Business-specific interfaces
- Dashboard components
- Data visualization

### Week 3: Polish & Testing
- Status indicators and feedback
- Edge cases and error states
- Accessibility testing

### Week 4: Documentation
- Complete design system documentation
- Component usage examples
- Developer guidelines

## 🚀 Next Steps

1. **Run the standardization script** on existing components
2. **Update high-priority components** manually for quality
3. **Test visual consistency** across all pages
4. **Update documentation** with new colour standards
5. **Train team** on new colour usage guidelines

This checklist ensures VentureDirection maintains a consistent, professional blue and white brand identity across all components and interfaces.
