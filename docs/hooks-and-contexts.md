# VentureDirection Hooks & Contexts Documentation

## 📚 Overview

This document provides comprehensive documentation for all custom hooks and contexts in the VentureDirection platform. These tools provide seamless integration between authentication, business context, user management, and permissions.

## 🗂️ Documentation Index

### 🔐 Authentication & User Management
- [`useVentureUser`](./hooks/useVentureUser.md) - Main user hook bridging auth providers with internal user model
- [`useVentureUserBasic`](./hooks/useVentureUser.md#useventureuserbasic) - Simplified user hook without business context
- [`usePermission`](./hooks/useVentureUser.md#usepermission) - Single permission check hook
- [`useRole`](./hooks/useVentureUser.md#userole) - Single role check hook

### 🏢 Business Context Management
- [`useBusiness`](./hooks/useBusiness.md) - Global business context and switching
- [`useBusinessUser`](./hooks/useBusinessUser.md) - Business-scoped user context (requires BusinessUserProvider)
- [`useBusinessPermissions`](./hooks/useBusinessPermissions.md) - Business-scoped permission checks
- [`useBusinessContext`](./hooks/useBusinessPermissions.md#usebusinesscontext) - Business context information
- [`useIsInBusinessContext`](./hooks/useBusinessUser.md#useisinbusinesscontext) - Check if within business context

### 📋 Onboarding & Setup
- [`useOnboarding`](./hooks/useOnboarding.md) - Business onboarding progress tracking
- [`useLastBusiness`](./hooks/useLastBusiness.md) - Last accessed business for smart defaults
- [`useInitializeBusiness`](./hooks/useLastBusiness.md#useinitializebusiness) - Initialize business context

### 🎯 Specialized Business Hooks
- [`useBusinessRole`](./hooks/useBusinessPermissions.md#usebusinessrole) - Check specific business role
- [`useIsBusinessOwner`](./hooks/useBusinessPermissions.md#useisbusinessowner) - Owner check
- [`useIsBusinessAdmin`](./hooks/useBusinessPermissions.md#useisbusinessadmin) - Admin check
- [`useBusinessFeatureAccess`](./hooks/useBusinessPermissions.md#usebusinessfeatureaccess) - Feature access checks

### 🔄 Context Providers
- [`BusinessProvider`](./contexts/business-context.md) - Global business management context
- [`BusinessUserProvider`](./contexts/business-user-context.md) - Business-scoped user context

## 🎯 Quick Reference

### Most Common Patterns

```typescript
// Global user context (works anywhere)
const { user, hasPermission, hasRole } = useVentureUser()

// Business-scoped context (requires BusinessUserProvider)
const { businessName, userRole, isOwner, canManageUsers } = useBusinessUser()

// Business management
const { currentBusiness, businesses, switchBusiness } = useBusiness()

// Permission checks
const { canViewFinance, canManageUsers, isAdmin } = useBusinessPermissions()

// Onboarding
const { onboardingStatus, updateProgress } = useOnboarding()
```

### Context Hierarchy

```
App
├── BusinessProvider (global business management)
│   ├── Dashboard (business selection)
│   └── BusinessUserProvider (business-scoped context)
│       ├── BusinessHeader
│       ├── BusinessSidebar  
│       └── Business Pages (orders, finance, etc.)
```

## 🔧 Implementation Guidelines

### 1. **Hook Selection Guide**

| Use Case | Recommended Hook | Context Required |
|----------|------------------|------------------|
| Basic user info | `useVentureUserBasic()` | None |
| Global permissions | `useVentureUser()` | None |
| Business-scoped features | `useBusinessUser()` | BusinessUserProvider |
| Business management | `useBusiness()` | BusinessProvider |
| Permission-heavy components | `useBusinessPermissions()` | BusinessUserProvider |
| Onboarding flows | `useOnboarding()` | BusinessProvider |

### 2. **Performance Best Practices**

```typescript
// ✅ Good: Use business-scoped hooks in business context
function BusinessPage() {
  const { canManageUsers } = useBusinessUser() // Single context call
  return <UserManagement />
}

// ❌ Avoid: Multiple permission checks
function BusinessPage() {
  const { hasPermission } = useVentureUser()
  const canManage = hasPermission('users.manage', businessId) // Requires businessId
  return <UserManagement />
}
```

### 3. **Error Handling Patterns**

```typescript
// Hook with error handling
const { user, isLoading, error, refetch } = useVentureUser()

if (isLoading) return <LoadingSpinner />
if (error) return <ErrorMessage error={error} onRetry={refetch} />
if (!user) return <LoginPrompt />

return <UserInterface />
```

## 🚨 Common Pitfalls

### 1. **Context Requirements**
```typescript
// ❌ Will throw error - useBusinessUser requires BusinessUserProvider
function MyComponent() {
  const { businessName } = useBusinessUser() // Error!
}

// ✅ Correct usage
<BusinessUserProvider businessId="123">
  <MyComponent /> {/* Now useBusinessUser works */}
</BusinessUserProvider>
```

### 2. **Permission Scope**
```typescript
// ❌ Global permission check (may not work as expected)
const { hasPermission } = useVentureUser()
const canManage = hasPermission('users.manage') // Which business?

// ✅ Business-scoped permission check
const { hasPermission } = useBusinessUser()
const canManage = hasPermission('users.manage') // Current business
```

### 3. **Hook Dependencies**
```typescript
// ❌ Using business hooks without business context
const { currentBusiness } = useBusiness()
const { businessName } = useBusinessUser() // May fail if no business selected

// ✅ Proper dependency checking
const { currentBusiness } = useBusiness()
if (!currentBusiness) return <SelectBusiness />

return (
  <BusinessUserProvider businessId={currentBusiness.id}>
    <BusinessWorkspace />
  </BusinessUserProvider>
)
```

## 📖 Detailed Documentation

Each hook and context has detailed documentation with:
- **Purpose & Use Cases**
- **Parameters & Return Values**
- **TypeScript Interfaces**
- **Usage Examples**
- **Performance Considerations**
- **Error Handling**
- **Integration Patterns**

Click on any hook name above to view its detailed documentation.

## 🔄 Migration Guide

### From Old Patterns

```typescript
// Old: Manual business ID passing
const user = useUser()
const permissions = usePermissions(businessId)
const canManage = permissions.includes('users.manage')

// New: Context-aware hooks
const { canManageUsers } = useBusinessUser()
```

### Adding New Business Features

```typescript
// 1. Wrap page with BusinessUserProvider (usually in layout)
<BusinessUserProvider businessId={businessId}>
  <NewFeaturePage />
</BusinessUserProvider>

// 2. Use business context in components
function NewFeaturePage() {
  const { hasPermission, isAdmin } = useBusinessUser()
  
  if (!hasPermission('new-feature.view')) {
    return <AccessDenied />
  }
  
  return <NewFeatureContent />
}
```

## 🎯 Best Practices Summary

1. **Use the most specific hook for your use case**
2. **Leverage context providers for business-scoped features**
3. **Handle loading and error states consistently**
4. **Prefer business-scoped hooks in business contexts**
5. **Document custom permission requirements**
6. **Test permission boundaries thoroughly**
7. **Use TypeScript for better developer experience**
