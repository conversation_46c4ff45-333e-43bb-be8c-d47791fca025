import { BusinessContextService } from '../business-context-service'
import { prisma } from '@/apps/web/db'

// Mock Prisma
jest.mock('@/apps/web/db', () => ({
  prisma: {
    userBusinessMembership: {
      findMany: jest.fn(),
      findFirst: jest.fn(),
      update: jest.fn(),
      updateMany: jest.fn(),
      upsert: jest.fn(),
    },
    userBusinessContext: {
      findUnique: jest.fn(),
      upsert: jest.fn(),
    },
    businessGroup: {
      findMany: jest.fn(),
    },
    business: {
      findUnique: jest.fn(),
    },
    roleAssignment: {
      findMany: jest.fn(),
    },
  }
}))

const mockedPrisma = prisma as jest.Mocked<typeof prisma>

describe('BusinessContextService', () => {
  let service: BusinessContextService

  beforeEach(() => {
    jest.clearAllMocks()
    service = new BusinessContextService()
  })

  describe('getUserSessionContext', () => {
    it('should return complete user session context', async () => {
      const userId = 'user-123'
      const mockMemberships = [
        {
          id: 'membership-1',
          userId,
          businessId: 'business-1',
          businessGroupId: 'group-1',
          roleTitle: 'Owner',
          isActive: true,
          lastAccessedAt: new Date('2024-01-15'),
          business: {
            id: 'business-1',
            name: 'Primary Business',
            type: 'Corporation',
            businessWithGroupRelationships: [
              {
                businessGroup: {
                  id: 'group-1',
                  name: 'Main Group',
                  description: 'Primary business group'
                }
              }
            ]
          },
          businessGroup: {
            id: 'group-1',
            name: 'Main Group',
            description: 'Primary business group'
          }
        }
      ]

      const mockContext = {
        userId,
        activeBusinessId: 'business-1',
        activeBusinessGroupId: 'group-1'
      }

      mockedPrisma.userBusinessMembership.findMany.mockResolvedValue(mockMemberships as any)
      mockedPrisma.userBusinessContext.findUnique.mockResolvedValue(mockContext as any)

      const result = await service.getUserSessionContext(userId)

      expect(mockedPrisma.userBusinessMembership.findMany).toHaveBeenCalledWith({
        where: {
          userId,
          isActive: true,
        },
        include: {
          business: {
            include: {
              businessWithGroupRelationships: {
                include: {
                  businessGroup: true,
                },
              },
            },
          },
          businessGroup: true,
        },
        orderBy: {
          lastAccessedAt: 'desc',
        },
      })

      expect(mockedPrisma.userBusinessContext.findUnique).toHaveBeenCalledWith({
        where: { userId }
      })

      expect(result.userId).toBe(userId)
      expect(result.activeBusinessId).toBe('business-1')
      expect(result.businesses).toHaveLength(1)
      expect(result.businesses[0].id).toBe('business-1')
    })

    it('should handle user with no business memberships', async () => {
      const userId = 'user-no-business'
      mockedPrisma.userBusinessMembership.findMany.mockResolvedValue([])
      mockedPrisma.userBusinessContext.findUnique.mockResolvedValue(null)

      const result = await service.getUserSessionContext(userId)

      expect(result.userId).toBe(userId)
      expect(result.businesses).toEqual([])
      expect(result.businessGroups).toEqual([])
      expect(result.activeBusinessId).toBeNull()
    })

    it('should handle database errors', async () => {
      const userId = 'user-123'
      const dbError = new Error('Database connection failed')
      mockedPrisma.userBusinessMembership.findMany.mockRejectedValue(dbError)

      await expect(service.getUserSessionContext(userId))
        .rejects.toThrow('Database connection failed')
    })
  })

  describe('switchBusiness', () => {
    it('should switch user business context successfully', async () => {
      const userId = 'user-123'
      const businessId = 'business-123'
      const mockMembership = {
        userId,
        businessId,
        businessGroupId: 'group-123',
        isActive: true
      }

      mockedPrisma.userBusinessMembership.findFirst.mockResolvedValue(mockMembership as any)
      mockedPrisma.userBusinessMembership.updateMany.mockResolvedValue({ count: 1 } as any)
      mockedPrisma.userBusinessContext.upsert.mockResolvedValue({} as any)

      await service.switchBusiness(userId, businessId)

      expect(mockedPrisma.userBusinessMembership.findFirst).toHaveBeenCalledWith({
        where: {
          userId,
          businessId,
          isActive: true
        }
      })

      expect(mockedPrisma.userBusinessContext.upsert).toHaveBeenCalled()
    })

    it('should throw error when user has no access to business', async () => {
      const userId = 'user-no-access'
      const businessId = 'business-123'

      mockedPrisma.userBusinessMembership.findFirst.mockResolvedValue(null)

      await expect(service.switchBusiness(userId, businessId))
        .rejects.toThrow('Access denied to this business')
    })
  })

  describe('requireBusinessAccess', () => {
    it('should pass when user has access', async () => {
      const userId = 'user-123'
      const businessId = 'business-123'
      const mockMembership = {
        userId,
        businessId,
        isActive: true
      }

      mockedPrisma.userBusinessMembership.findFirst.mockResolvedValue(mockMembership as any)

      await expect(service.requireBusinessAccess(userId, businessId)).resolves.not.toThrow()
    })

    it('should throw error when user has no access', async () => {
      const userId = 'user-no-access'
      const businessId = 'business-123'

      mockedPrisma.userBusinessMembership.findFirst.mockResolvedValue(null)

      await expect(service.requireBusinessAccess(userId, businessId))
        .rejects.toThrow('Access denied to this business')
    })
  })
})
