// import { ThemeProvider as NextThemeProvider } from 'next-themes'
// import { ThemeProvider as NativeThemeProvider, DefaultTheme, DarkTheme } from '@react-navigation/native'
// import { useColorScheme } from 'react-native'

// export function Provider({ children }: { children: React.ReactNode }) {
//   const scheme = useColorScheme()
  
//   return (
//     <NextThemeProvider
//       attribute="class"
//       defaultTheme={scheme || 'light'}
//       enableSystem
//       disableTransitionOnChange
//     >
//       <NativeThemeProvider value={scheme === 'dark' ? DarkTheme : DefaultTheme}>
//         {children}
//       </NativeThemeProvider>
//     </NextThemeProvider>
//   )
// }
