import type { VentureUser, VentureUserBusinessContext } from './venture-user'

export interface BusinessUserContextData {
  // Core user data scoped to current business
  user: VentureUser | null
  businessUser: VentureUserBusinessContext | null
  
  // Loading and error states
  isLoading: boolean
  error: string | null
  
  // Business context
  businessId: string
  businessName: string | null
  userRole: string | null
  
  // Permission helpers (scoped to current business)
  hasPermission: (permission: string) => boolean
  hasRole: (role: string) => boolean
  canAccess: (resource: string, action: string) => boolean
  
  // Role checks (scoped to current business)
  isOwner: boolean
  isAdmin: boolean
  isMember: boolean
  canManageUsers: boolean
  canManageSettings: boolean
  canViewFinance: boolean
  canManageOrders: boolean
  
  // Business-specific permissions
  permissions: string[]
  roles: string[]
  
  // Actions
  refetch: () => Promise<void>
  refresh: () => Promise<void>
}

export interface BusinessUserProviderProps {
  businessId: string
  children: React.ReactNode
  fallback?: React.ReactNode
  loadingFallback?: React.ReactNode
  errorFallback?: React.ReactNode
}

// Common business permissions
export const BUSINESS_PERMISSIONS = {
  // User management
  USERS_VIEW: 'users.view',
  USERS_MANAGE: 'users.manage',
  USERS_INVITE: 'users.invite',
  USERS_REMOVE: 'users.remove',
  
  // Business settings
  SETTINGS_VIEW: 'settings.view',
  SETTINGS_MANAGE: 'settings.manage',
  BUSINESS_MANAGE: 'business.manage',
  
  // Financial
  FINANCE_VIEW: 'finance.view',
  FINANCE_MANAGE: 'finance.manage',
  INVOICES_CREATE: 'invoices.create',
  PAYMENTS_MANAGE: 'payments.manage',
  
  // Orders & Inventory
  ORDERS_VIEW: 'orders.view',
  ORDERS_MANAGE: 'orders.manage',
  INVENTORY_VIEW: 'inventory.view',
  INVENTORY_MANAGE: 'inventory.manage',
  
  // CRM
  CRM_VIEW: 'crm.view',
  CRM_MANAGE: 'crm.manage',
  CUSTOMERS_MANAGE: 'customers.manage',
  
  // Documents & Compliance
  DOCUMENTS_VIEW: 'documents.view',
  DOCUMENTS_MANAGE: 'documents.manage',
  COMPLIANCE_VIEW: 'compliance.view',
  COMPLIANCE_MANAGE: 'compliance.manage',
  
  // Analytics & Reports
  ANALYTICS_VIEW: 'analytics.view',
  REPORTS_VIEW: 'reports.view',
  REPORTS_EXPORT: 'reports.export',
} as const

// Common business roles
export const BUSINESS_ROLES = {
  OWNER: 'owner',
  ADMIN: 'admin',
  MANAGER: 'manager',
  MEMBER: 'member',
  VIEWER: 'viewer',
} as const
