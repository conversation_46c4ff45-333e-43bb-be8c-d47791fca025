// Central API exports
// This file makes it easy to import all API functions from one place
// and will simplify the migration to external NestJS API
import { apiClient } from './client'

export { apiClient, ApiClient } from './client'
export { API_ENDPOINTS, getApiConfig } from './endpoints'

// API classes
export { business<PERSON>pi, BusinessApi } from './business-api'
export { complianceApi, ComplianceApi } from './compliance-api'
export { authApi, AuthApi } from './auth-api'

// Re-export types for convenience
export type * from '@/apps/web/library/types'
// export type * from '@/lib/types/compliance'
// export type * from '@/lib/types/user'
// export type * from '@/lib/types/document'

// Migration helper - when moving to external API, update this configuration
export const API_CONFIG = {
  // Set this to true when migrating to external NestJS API
  USE_EXTERNAL_API: process.env.NEXT_PUBLIC_USE_EXTERNAL_API === 'true',
  
  // External API URL (will be set when migrating)
  EXTERNAL_API_URL: process.env.NEXT_PUBLIC_API_URL,
  
  // API version for future compatibility
  API_VERSION: 'v1',
  
  // Timeout settings
  REQUEST_TIMEOUT: 10000,
  
  // Retry settings
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000,
}

// Helper function to initialize API client with external URL
export const initializeExternalApi = (baseURL: string, authToken?: string) => {
  apiClient.updateBaseURL(baseURL)
  if (authToken) {
    apiClient.setAuthToken(authToken)
  }
}
