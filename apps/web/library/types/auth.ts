export interface SignIn {
    email: string,
    password: string,
}


export interface SignOut {
    userId: string,
}

export interface CheckAuthData {
    id: string,
    clerkUserId: string,
    firstName: string,
    lastName: string,
    phone: string,
    language: string,
    timezone: string,
    activeBusinessId: string | null,
    activeBusinessGroupId: string | null,
    accessibleBusinessIds: string[],
    accessibleBusinessGroupIds: string[],
    emailAddresses?: string[],
    userSettings?: Record<string, any>,
}