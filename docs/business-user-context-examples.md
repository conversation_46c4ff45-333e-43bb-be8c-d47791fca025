# BusinessUser Context Provider Examples

## 🎯 Overview

The `BusinessUserContext` provides business-scoped user data and permissions to all child components within a business workspace. It eliminates redundant API calls and provides seamless access to business-specific user context.

## 🏗️ Basic Setup

### 1. Wrap Business Pages with Provider

```typescript
import { BusinessWorkspaceLayout } from '@/components/layouts/businessWorkspaceLayout'

export default function BusinessPage() {
  return (
    <BusinessWorkspaceLayout>
      {/* All child components now have access to business user context */}
      <BusinessDashboard />
      <BusinessSettings />
      <UserManagement />
    </BusinessWorkspaceLayout>
  )
}
```

### 2. Use Context in Child Components

```typescript
import { useBusinessUser } from '@/lib/contexts/business-user-context'

function BusinessDashboard() {
  const { 
    user,
    businessName,
    userRole,
    isOwner,
    isAdmin,
    hasPermission,
    canManageUsers
  } = useBusinessUser()

  return (
    <div>
      <h1>Welcome to {businessName}</h1>
      <p>Your role: {userRole}</p>
      
      {isOwner && <OwnerDashboard />}
      {isAdmin && <AdminPanel />}
      {canManageUsers && <UserManagementButton />}
    </div>
  )
}
```

## 🔒 Permission-Based UI

### Component-Level Permissions

```typescript
function BusinessFeatures() {
  const { hasPermission, isAdmin, isOwner } = useBusinessUser()

  return (
    <div>
      {/* Permission-based rendering */}
      {hasPermission('finance.view') && (
        <FinancialDashboard />
      )}
      
      {hasPermission('users.manage') && (
        <UserManagementPanel />
      )}
      
      {/* Role-based rendering */}
      {isAdmin && (
        <AdminSettings />
      )}
      
      {isOwner && (
        <BusinessTransferOwnership />
      )}
    </div>
  )
}
```

### Navigation with Permissions

```typescript
function BusinessNavigation() {
  const { hasPermission, isAdmin } = useBusinessUser()

  const navigationItems = [
    { name: 'Dashboard', href: '/dashboard', show: true },
    { name: 'Orders', href: '/orders', show: hasPermission('orders.view') },
    { name: 'Finance', href: '/finance', show: hasPermission('finance.view') },
    { name: 'Users', href: '/users', show: hasPermission('users.view') },
    { name: 'Settings', href: '/settings', show: isAdmin },
  ]

  return (
    <nav>
      {navigationItems
        .filter(item => item.show)
        .map(item => (
          <Link key={item.name} href={item.href}>
            {item.name}
          </Link>
        ))
      }
    </nav>
  )
}
```

## 🎣 Specialized Hooks

### Business Permissions Hook

```typescript
import { useBusinessPermissions } from '@/lib/hooks/useBusinessPermissions'

function UserManagement() {
  const {
    canViewUsers,
    canManageUsers,
    canInviteUsers,
    canRemoveUsers,
    isOwner,
    isAdmin
  } = useBusinessPermissions()

  return (
    <div>
      {canViewUsers && <UserList />}
      
      {canInviteUsers && (
        <Button>Invite User</Button>
      )}
      
      {canManageUsers && (
        <Button>Manage Roles</Button>
      )}
      
      {canRemoveUsers && (
        <Button variant="destructive">Remove User</Button>
      )}
    </div>
  )
}
```

### Business Context Hook

```typescript
import { useBusinessContext } from '@/lib/hooks/useBusinessPermissions'

function BusinessInfo() {
  const {
    businessId,
    businessName,
    userRole,
    permissions,
    roles,
    hasAnyPermissions,
    isActiveUser
  } = useBusinessContext()

  return (
    <div>
      <h2>{businessName}</h2>
      <p>Role: {userRole}</p>
      <p>Permissions: {permissions.length}</p>
      <p>Active: {isActiveUser ? 'Yes' : 'No'}</p>
      
      {!hasAnyPermissions && (
        <div className="text-red-600">
          No permissions assigned. Contact administrator.
        </div>
      )}
    </div>
  )
}
```

## 🎨 Layout Integration

### Business Header with Context

```typescript
function BusinessHeader() {
  const { 
    businessName, 
    userRole, 
    isOwner, 
    isAdmin,
    canManageUsers 
  } = useBusinessUser()

  return (
    <header>
      <div className="flex items-center justify-between">
        <div>
          <h1>{businessName}</h1>
          <div className="flex items-center gap-2">
            <span>{userRole}</span>
            {isOwner && <Badge>Owner</Badge>}
            {isAdmin && <Badge>Admin</Badge>}
          </div>
        </div>
        
        <div className="flex gap-2">
          {canManageUsers && (
            <Button>Manage Team</Button>
          )}
          <UserMenu />
        </div>
      </div>
    </header>
  )
}
```

### Business Sidebar with Permissions

```typescript
function BusinessSidebar() {
  const { hasPermission, isAdmin, isOwner } = useBusinessUser()

  const menuItems = [
    { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard, show: true },
    { name: 'Orders', href: '/orders', icon: ShoppingCart, show: hasPermission('orders.view') },
    { name: 'Finance', href: '/finance', icon: DollarSign, show: hasPermission('finance.view') },
    { name: 'Analytics', href: '/analytics', icon: BarChart, show: hasPermission('analytics.view') },
    { name: 'Settings', href: '/settings', icon: Settings, show: isAdmin },
    { name: 'Business Groups', href: '/groups', icon: Building, show: isOwner },
  ]

  return (
    <aside>
      <nav>
        {menuItems
          .filter(item => item.show)
          .map(item => (
            <SidebarItem key={item.name} {...item} />
          ))
        }
      </nav>
    </aside>
  )
}
```

## 🔄 Real-World Examples

### Order Management with Permissions

```typescript
function OrderManagement() {
  const { 
    hasPermission, 
    canManageOrders, 
    canViewOrders,
    isAdmin 
  } = useBusinessUser()

  if (!canViewOrders) {
    return <AccessDenied feature="Orders" />
  }

  return (
    <div>
      <OrderList />
      
      {canManageOrders && (
        <div>
          <Button>Create Order</Button>
          <Button>Bulk Actions</Button>
        </div>
      )}
      
      {hasPermission('orders.export') && (
        <Button>Export Orders</Button>
      )}
      
      {isAdmin && (
        <Button>Order Settings</Button>
      )}
    </div>
  )
}
```

### Financial Dashboard with Role Checks

```typescript
function FinancialDashboard() {
  const {
    canViewFinance,
    canManageFinance,
    canCreateInvoices,
    canManagePayments,
    isOwner
  } = useBusinessPermissions()

  if (!canViewFinance) {
    return <AccessDenied feature="Finance" />
  }

  return (
    <div>
      <FinancialOverview />
      
      {canCreateInvoices && (
        <InvoiceCreation />
      )}
      
      {canManagePayments && (
        <PaymentManagement />
      )}
      
      {canManageFinance && (
        <FinancialSettings />
      )}
      
      {isOwner && (
        <FinancialReports />
      )}
    </div>
  )
}
```

### User Profile with Business Context

```typescript
function UserProfile() {
  const { 
    user,
    businessName,
    userRole,
    permissions,
    businessUser,
    refresh
  } = useBusinessUser()

  return (
    <div>
      <div className="profile-header">
        <img src={user.avatar} alt={user.displayName} />
        <div>
          <h2>{user.displayName}</h2>
          <p>{user.email}</p>
        </div>
      </div>
      
      <div className="business-context">
        <h3>Business Context</h3>
        <p>Business: {businessName}</p>
        <p>Role: {userRole}</p>
        <p>Permissions: {permissions.length}</p>
        <p>Last Access: {businessUser?.lastAccessedAt}</p>
      </div>
      
      <Button onClick={refresh}>
        Refresh Context
      </Button>
    </div>
  )
}
```

## 🚀 Benefits

### 1. **No Redundant API Calls**
- Single fetch per business context
- Shared across all child components
- Automatic caching and refresh

### 2. **Seamless Permission Checks**
- Business-scoped permissions
- Role-based access control
- Real-time permission updates

### 3. **Clean Component Code**
- No prop drilling
- Consistent API across components
- TypeScript support

### 4. **Performance Optimized**
- Context-level caching
- Selective re-renders
- Efficient permission lookups

### 5. **Developer Experience**
- Simple hook interface
- Comprehensive error handling
- Rich debugging information
