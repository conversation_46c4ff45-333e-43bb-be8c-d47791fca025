import { prisma } from '@/apps/web/db';
import { BusinessRegistrationStatus, BusinessStatus, BusinessSystemStatus, UpdateBusinessData } from '../types';



export class BusinessService {
    async getUserBusinesses(userId: string): Promise<any> {
      const memberships = await prisma.userBusinessMembership.findMany({
        where: {
          userId,
          isActive: true,
        },
        include: {
          business: {
            include: {
              country: true,
              businessIndustries: {
                include: {
                  industry: true
                }
              },
              _count: {
                select: {
                  compliance: true,
                  notes: true,
                  secureVaults: true
                }
              }
            }
          }
        },
        orderBy: {
          lastAccessedAt: 'desc'
        }
      })

      return memberships.map((membership: any) => membership.business)
    }

    async getBusinessById(businessId: string, userId?: string): Promise<any> {
      // Check user access if userId is provided
      if (userId) {
        const hasAccess = await this.checkUserBusinessAccess(userId, businessId)
        if (!hasAccess) {
          throw new Error('Access denied: User does not have access to this business')
        }
      }

      return await prisma.business.findUnique({
        where: { id: businessId },
        include: {
          country: true,
          businessIndustries: {
            include: {
              industry: true
            }
          },
          compliance: {
            where: {
              status: { in: ['PENDING', 'IN_PROGRESS'] }
            },
            orderBy: { dueDate: 'asc' },
            take: 5
          },
          _count: {
            select: {
              compliance: true,
              notes: true,
              secureVaults: true
            }
          }
        }
      })
    }

    async createBusiness(userId: string, data: any): Promise<any> {
      return await prisma.$transaction(async (tx: any) => {
        // Create the business
        const business = await tx.business.create({
          data: {
            ...data,
            businessStatus: 'ACTIVE' as BusinessStatus,
            systemStatus: 'ACTIVE' as BusinessSystemStatus,
            registrationStatus: 'PENDING' as BusinessRegistrationStatus
          }
        })

        // Create owner membership
        await tx.userBusinessMembership.create({
          data: {
            userId,
            businessId: business.id,
            roleTitle: 'OWNER',
            isActive: true,
            lastAccessedAt: new Date()
          }
        })

        return business
      })
    }

    async updateBusiness(businessId: string, userId: string, data: UpdateBusinessData): Promise<any> {
      // Check user access
      const hasAccess = await this.checkUserBusinessAccess(userId, businessId)
      if (!hasAccess) {
        throw new Error('Access denied: User does not have access to this business')
      }

      return await prisma.business.update({
        where: { id: businessId },
        data,
        include: {
          country: true,
          businessIndustries: {
            include: {
              industry: true
            }
          }
        }
      })
    }

    async deleteBusiness(businessId: string, userId: string): Promise<void> {
      // Check if user is owner
      const isOwner = await this.checkUserBusinessOwnership(userId, businessId)
      if (!isOwner) {
        throw new Error('Access denied: Only business owners can delete businesses')
      }

      await prisma.$transaction(async (tx: any) => {
        // Soft delete by updating status
        await tx.business.update({
          where: { id: businessId },
          data: {
            businessStatus: 'CLOSED',
            systemStatus: 'DEREGISTERED'
          }
        })

        // Deactivate all memberships
        await tx.userBusinessMembership.updateMany({
          where: { businessId },
          data: { isActive: false }
        })
      })
    }

    async checkUserBusinessAccess(userId: string, businessId: string): Promise<boolean> {
      const membership = await prisma.userBusinessMembership.findFirst({
        where: {
          userId,
          businessId,
          isActive: true
        }
      })

      return !!membership
    }

    private async checkUserBusinessOwnership(userId: string, businessId: string): Promise<boolean> {
      const membership = await prisma.userBusinessMembership.findFirst({
        where: {
          userId,
          businessId,
          roleTitle: 'OWNER',
          isActive: true
        }
      })

      return !!membership
    }
}
export const businessService = new BusinessService();
