/**
 * Jest Setup for React Native Packages (UI & App)
 *
 * Setup for React Native component testing with simplified mocking
 */

// Mock environment variables for packages
Object.defineProperty(process.env, 'NODE_ENV', { value: 'test' })

// Mock nativewind
jest.mock('nativewind', () => ({
  cssInterop: jest.fn(),
  styled: jest.fn(() => (component: any) => component),
}))

// Setup test timeout
jest.setTimeout(10000)

// Clear all mocks before each test
beforeEach(() => {
  jest.clearAllMocks()
})
