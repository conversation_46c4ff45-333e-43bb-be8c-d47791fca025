// Common types used across the application

// API Response wrapper
export interface ApiResponse<T = any> {
  data: T
  success: boolean,
  statusCode: number ,
  message?: string,
  timestamp?: string,
  details?: Record<string, any>
}

// Paginated response
export interface PaginatedResponse<T = any> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
  filters?: Record<string, any>
}

// Error response
// export interface ErrorResponse {
//   error: string
//   message: string
//   statusCode: number
//   timestamp: string
//   path?: string
//   details?: Record<string, any>
// }

// Sort options
export interface SortOption {
  field: string
  direction: 'asc' | 'desc'
  label: string
}

// Filter option
export interface FilterOption {
  value: string
  label: string
  count?: number
  disabled?: boolean
}

// Search filters
export interface SearchFilters {
  query?: string
  filters: Record<string, any>
  sort?: {
    field: string
    direction: 'asc' | 'desc'
  }
  pagination?: {
    page: number
    limit: number
  }
}

// Dashboard widget
export interface DashboardWidget {
  id: string
  type: 'chart' | 'stat' | 'list' | 'table' | 'calendar'
  title: string
  description?: string
  size: 'small' | 'medium' | 'large'
  position: {
    x: number
    y: number
    w: number
    h: number
  }
  data?: any
  config?: Record<string, any>
  refreshInterval?: number
  lastUpdated?: Date
}

// Notification
export interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  timestamp: Date
  read: boolean
  actionUrl?: string
  actionLabel?: string
  metadata?: Record<string, any>
}

// Activity log entry
export interface ActivityLogEntry {
  id: string
  userId: string
  userName: string
  action: string
  resource: string
  resourceId: string
  resourceName?: string
  description: string
  metadata?: Record<string, any>
  timestamp: Date
  ipAddress?: string
  userAgent?: string
}

// File upload progress
export interface FileUploadProgress {
  fileId: string
  fileName: string
  fileSize: number
  uploadedBytes: number
  progress: number
  status: 'pending' | 'uploading' | 'completed' | 'error'
  error?: string
  url?: string
}

// Audit trail entry
export interface AuditTrailEntry {
  id: string
  entityType: string
  entityId: string
  action: 'create' | 'update' | 'delete' | 'view'
  userId: string
  userName: string
  changes?: {
    field: string
    oldValue: any
    newValue: any
  }[]
  timestamp: Date
  ipAddress?: string
  userAgent?: string
}

// System health status
export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy'
  services: {
    database: 'up' | 'down' | 'degraded'
    storage: 'up' | 'down' | 'degraded'
    email: 'up' | 'down' | 'degraded'
    auth: 'up' | 'down' | 'degraded'
  }
  metrics: {
    responseTime: number
    uptime: number
    memoryUsage: number
    cpuUsage: number
  }
  timestamp: Date
}

// Feature flag
export interface FeatureFlag {
  key: string
  name: string
  description: string
  enabled: boolean
  rolloutPercentage: number
  conditions?: {
    userIds?: string[]
    businessIds?: string[]
    countries?: string[]
    plans?: string[]
  }
}

// Integration status
export interface IntegrationStatus {
  id: string
  name: string
  type: string
  status: 'active' | 'inactive' | 'error' | 'pending'
  lastSync?: Date
  nextSync?: Date
  errorMessage?: string
  config: Record<string, any>
}

// Export/Import job
export interface ExportImportJob {
  id: string
  type: 'export' | 'import'
  format: 'csv' | 'xlsx' | 'json' | 'pdf'
  entityType: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  totalRecords?: number
  processedRecords?: number
  fileUrl?: string
  errorMessage?: string
  createdBy: string
  createdAt: Date
  completedAt?: Date
}
