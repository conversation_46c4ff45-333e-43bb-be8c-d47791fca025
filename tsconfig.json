{"extends": "expo/tsconfig.base", "ts-node": {"compilerOptions": {"module": "CommonJS"}}, "compilerOptions": {"baseUrl": ".", "target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"], "@/ui/*": ["./packages/ui/*"], "@/app/*": ["./packages/app/*"], "@/lib/*": ["./packages/lib/*"]}, "typeRoots": ["./node_modules/@types", "./types"]}, "include": [".expo/types/**/*.ts", "expo-env.d.ts", "next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "nativewind-env.d.ts"], "exclude": ["node_modules", "**/__tests__/**", "**/__test__/**", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx", "**/jest.setup.ts", "**/jest.setup.js", "**/jest.config.ts", "**/jest.config.js", ".next", "dist", "build"]}