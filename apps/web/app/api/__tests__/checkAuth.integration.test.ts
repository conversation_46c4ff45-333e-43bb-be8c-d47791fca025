import { NextRequest } from 'next/server'
import { GET } from '../checkAuth/route'
import { getVentureUser } from '@/apps/web/library/services/venture-user-service'

// Mock the venture user service
jest.mock('@/apps/web/library/services/venture-user-service')
const mockedGetVentureUser = getVentureUser as jest.MockedFunction<typeof getVentureUser>

// Mock NextRequest
const createMockRequest = (options: { url?: string; headers?: Record<string, string> } = {}) => {
  return {
    url: options.url || 'http://localhost:3000/api/checkAuth',
    headers: new Headers(options.headers || {}),
    method: 'GET',
  } as NextRequest
}

describe('/api/checkAuth Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/checkAuth', () => {
    it('should return venture user data successfully', async () => {
      const mockVentureUser = {
        id: 'user-123',
        clerkUserId: 'clerk-456',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        language: 'en',
        timezone: 'UTC',
        activeBusinessId: 'business-789',
        activeBusinessGroupId: 'group-101',
        accessibleBusinessIds: ['business-789', 'business-202'],
        accessibleBusinessGroupIds: ['group-101'],
        emailAddresses: ['<EMAIL>'],
        userSettings: {
          theme: 'dark',
          notifications: true
        },
        roles: [
          {
            id: 'role-1',
            name: 'Business Owner',
            scope: 'BUSINESS',
            businessId: 'business-789'
          }
        ],
        permissions: [
          {
            id: 'perm-1',
            name: 'manage_business',
            resource: 'business',
            action: 'manage'
          }
        ]
      }

      mockedGetVentureUser.mockResolvedValue(mockVentureUser)

      const request = createMockRequest()
      const response = await GET(request)
      const responseData = await response.json()

      expect(mockedGetVentureUser).toHaveBeenCalledWith({
        includeRoles: true,
        includePermissions: true
      })

      expect(response.status).toBe(200)
      expect(responseData).toEqual(mockVentureUser)
    })

    it('should handle authentication errors', async () => {
      const authError = new Error('User not authenticated')
      mockedGetVentureUser.mockRejectedValue(authError)

      const request = createMockRequest()
      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(500)
      expect(responseData).toEqual({
        error: 'Failed to fetch venture user'
      })
    })

    it('should handle service errors gracefully', async () => {
      const serviceError = new Error('Database connection failed')
      mockedGetVentureUser.mockRejectedValue(serviceError)

      const request = createMockRequest()
      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(500)
      expect(responseData).toEqual({
        error: 'Failed to fetch venture user'
      })
    })

    it('should handle missing user data', async () => {
      mockedGetVentureUser.mockResolvedValue(null)

      const request = createMockRequest()
      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData).toBeNull()
    })

    it('should handle partial user data', async () => {
      const partialUser = {
        id: 'user-123',
        clerkUserId: 'clerk-456',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: null,
        language: 'en',
        timezone: 'UTC',
        activeBusinessId: null,
        activeBusinessGroupId: null,
        accessibleBusinessIds: [],
        accessibleBusinessGroupIds: [],
        emailAddresses: ['<EMAIL>'],
        userSettings: {},
        roles: [],
        permissions: []
      }

      mockedGetVentureUser.mockResolvedValue(partialUser)

      const request = createMockRequest()
      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData).toEqual(partialUser)
    })

    it('should handle requests with different headers', async () => {
      const mockUser = {
        id: 'user-123',
        clerkUserId: 'clerk-456',
        firstName: 'Jane',
        lastName: 'Smith'
      }

      mockedGetVentureUser.mockResolvedValue(mockUser)

      const request = createMockRequest({
        headers: {
          'Authorization': 'Bearer test-token',
          'Content-Type': 'application/json',
          'User-Agent': 'Test Client'
        }
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData).toEqual(mockUser)
    })

    it('should handle concurrent requests', async () => {
      const mockUser = {
        id: 'user-123',
        clerkUserId: 'clerk-456',
        firstName: 'Concurrent',
        lastName: 'User'
      }

      mockedGetVentureUser.mockResolvedValue(mockUser)

      const request1 = createMockRequest()
      const request2 = createMockRequest()
      const request3 = createMockRequest()

      const [response1, response2, response3] = await Promise.all([
        GET(request1),
        GET(request2),
        GET(request3)
      ])

      expect(response1.status).toBe(200)
      expect(response2.status).toBe(200)
      expect(response3.status).toBe(200)

      expect(mockedGetVentureUser).toHaveBeenCalledTimes(3)
    })
  })

  describe('Error scenarios', () => {
    it('should handle timeout errors', async () => {
      const timeoutError = new Error('Request timeout')
      timeoutError.name = 'TimeoutError'
      mockedGetVentureUser.mockRejectedValue(timeoutError)

      const request = createMockRequest()
      const response = await GET(request)

      expect(response.status).toBe(500)
    })

    it('should handle validation errors', async () => {
      const validationError = new Error('Invalid user data')
      validationError.name = 'ValidationError'
      mockedGetVentureUser.mockRejectedValue(validationError)

      const request = createMockRequest()
      const response = await GET(request)

      expect(response.status).toBe(500)
    })
  })
})
