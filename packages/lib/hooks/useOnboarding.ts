// import { useState, useEffect, useCallback } from 'react'
// import { useBusiness } from '@/packages/lib/contexts/business-context'
// import type { BusinessOnboardingStatus, OnboardingFeatureModule } from '@/apps/web/library/types/onboarding'

// interface UseOnboardingReturn {
//   onboardingStatus: BusinessOnboardingStatus | null
//   isLoading: boolean
//   error: string | null
//   refreshStatus: () => Promise<void>
//   updateProgress: (module: OnboardingFeatureModule, completed: boolean) => Promise<void>
//   initializeOnboarding: () => Promise<void>
// }

// export function useOnboarding(): UseOnboardingReturn {
//   const { currentBusiness } = useBusiness()
//   const [onboardingStatus, setOnboardingStatus] = useState<BusinessOnboardingStatus | null>(null)
//   const [isLoading, setIsLoading] = useState(false)
//   const [error, setError] = useState<string | null>(null)

//   const fetchOnboardingStatus = useCallback(async () => {
//     if (!currentBusiness?.id) return

//     try {
//       setIsLoading(true)
//       setError(null)

//       const response = await fetch(`/api/business/onboarding-progress?businessId=${currentBusiness.id}`)
      
//       if (!response.ok) {
//         throw new Error('Failed to fetch onboarding status')
//       }

//       const status: BusinessOnboardingStatus = await response.json()
//       setOnboardingStatus(status)
//     } catch (err) {
//       console.error('Error fetching onboarding status:', err)
//       setError(err instanceof Error ? err.message : 'Unknown error')
//     } finally {
//       setIsLoading(false)
//     }
//   }, [currentBusiness?.id])

//   const updateProgress = useCallback(async (module: OnboardingFeatureModule, completed: boolean) => {
//     if (!currentBusiness?.id) return

//     try {
//       setError(null)

//       const response = await fetch('/api/business/onboarding-progress', {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({
//           businessId: currentBusiness.id,
//           featureModule: module,
//           completed,
//         }),
//       })

//       if (!response.ok) {
//         throw new Error('Failed to update onboarding progress')
//       }

//       const result = await response.json()
//       setOnboardingStatus(result.status)
//     } catch (err) {
//       console.error('Error updating onboarding progress:', err)
//       setError(err instanceof Error ? err.message : 'Unknown error')
//       throw err
//     }
//   }, [currentBusiness?.id])

//   const initializeOnboarding = useCallback(async () => {
//     if (!currentBusiness?.id) return

//     try {
//       setError(null)

//       const response = await fetch('/api/business/onboarding-progress', {
//         method: 'PUT',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({
//           businessId: currentBusiness.id,
//         }),
//       })

//       if (!response.ok) {
//         throw new Error('Failed to initialize onboarding')
//       }

//       const result = await response.json()
//       setOnboardingStatus(result.status)
//     } catch (err) {
//       console.error('Error initializing onboarding:', err)
//       setError(err instanceof Error ? err.message : 'Unknown error')
//       throw err
//     }
//   }, [currentBusiness?.id])

//   const refreshStatus = useCallback(async () => {
//     await fetchOnboardingStatus()
//   }, [fetchOnboardingStatus])

//   // Fetch status when business changes
//   useEffect(() => {
//     if (currentBusiness?.id) {
//       fetchOnboardingStatus()
//     } else {
//       setOnboardingStatus(null)
//     }
//   }, [currentBusiness?.id, fetchOnboardingStatus])

//   return {
//     onboardingStatus,
//     isLoading,
//     error,
//     refreshStatus,
//     updateProgress,
//     initializeOnboarding,
//   }
// }
